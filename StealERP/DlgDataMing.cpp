// DlgDataMing.cpp : 实现文件
//

#include "stdafx.h"
#include "StealERP.h"
#include "DlgDataMing.h"
#include "afxdialogex.h"
#include "DlgPayedWage.h"
#include "Dlg_ArrearsList.h"

// CDlgDataMing 对话框

IMPLEMENT_DYNAMIC(CDlgDataMing, CDialogEx)

CDlgDataMing::CDlgDataMing(CWnd* pParent /*=NULL*/)
	: CDialogEx(IDD_DlgDataMining, pParent)
{

}

CDlgDataMing::~CDlgDataMing()
{
}

void CDlgDataMing::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
}


BEGIN_MESSAGE_MAP(CDlgDataMing, CDialogEx)
	ON_BN_CLICKED(IDC_BTDArrears, &CDlgDataMing::OnBnClickedBtdarrears)
	ON_BN_CLICKED(IDC_BTDSZJS, &CDlgDataMing::OnBnClickedBtdszjs)
	ON_BN_CLICKED(IDC_BTNWage, &CDlgDataMing::OnBnClickedBtnwage)
END_MESSAGE_MAP()


// CDlgDataMing 消息处理程序


void CDlgDataMing::PostNcDestroy()
{
	// TODO: 在此添加专用代码和/或调用基类

	CDialogEx::PostNcDestroy();
	delete this;
}


void CDlgDataMing::OnOK()
{
	// TODO: 在此添加专用代码和/或调用基类

	CDialogEx::DestroyWindow();
}


void CDlgDataMing::OnCancel()
{
	// TODO: 在此添加专用代码和/或调用基类

	CDialogEx::DestroyWindow();
}


void CDlgDataMing::OnBnClickedBtdarrears()
{
	CDlg_ArrearsList dlg;
	dlg.DoModal();
}


void CDlgDataMing::OnBnClickedBtdszjs()
{
	// TODO: 在此添加控件通知处理程序代码
}


void CDlgDataMing::OnBnClickedBtnwage()
{
	CDlgPayedWage payedWage;
	payedWage.DoModal();
}
