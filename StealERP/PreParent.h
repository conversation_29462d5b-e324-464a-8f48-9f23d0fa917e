#pragma once
#include "PreView.h"
#include "afxwin.h"


// CPreParent 对话框

class CPreParent : public CDialogEx
{
	DECLARE_DYNAMIC(CPreParent)

public:
	void SetCallBackFun(PRINTPREVIEW pv, PRNINFO PrnInfo);
	CPreParent(CWnd* pParent = NULL);   // 标准构造函数
	virtual ~CPreParent();

	// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_PREPARENT };
#endif

protected:
	HICON m_hIcon;
	PRINTPREVIEW pDrawInfo;
	PRNINFO PrnInfo;
	CImageList m_ImageList;
	CToolBar m_wndtoolbar;
	CRect m_TbRect;
	CRect rcClient;
	CPreView *pPreView;
	int			m_nCount;		//共有多少行数据
	int			m_OneCount;	//每的行数
	int			m_PosPage;			//当前页
	int			m_nCountPage;		//其有多少页


	static HWND hPrvWnd;

	void UpdatePreViewWnd();


	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持

	DECLARE_MESSAGE_MAP()
public:
	virtual BOOL OnInitDialog();
	afx_msg void OnSize(UINT nType, int cx, int cy);
	afx_msg void OnPaint();
	afx_msg void OnTbtnFirst();
	afx_msg void OnTbtnLast();
	afx_msg void OnTbtnNext();
	afx_msg void OnTbtnPrevious();
	afx_msg void OnTbtnPrint();
	afx_msg void OnTbtnCancel();
	virtual BOOL DestroyWindow();
	virtual BOOL OnCommand(WPARAM wParam, LPARAM lParam);
	afx_msg void OnSysCommand(UINT nID, LPARAM lParam);
};