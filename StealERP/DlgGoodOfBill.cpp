// DlgGoodOfBill.cpp : 实现文件
//

#include "stdafx.h"
#include "StealERP.h"
#include "DlgGoodOfBill.h"
#include "afxdialogex.h"


// CDlgGoodOfBill 对话框

IMPLEMENT_DYNAMIC(CDlgGoodOfBill, CDialogEx)

CDlgGoodOfBill::CDlgGoodOfBill(CWnd* pParent /*=NULL*/)
	: CDialogEx(IDD_DLG_BillMsg, pParent)
{
	bIsWorded = false;
}



CDlgGoodOfBill::~CDlgGoodOfBill()
{
	for (int i = 0; i < m_pPrice.GetSize(); i++)
	{
		delete ((struct_UnitPrice*)m_pPrice[i]);
	}

}

void CDlgGoodOfBill::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_LSTGoods, m_list);
}


BEGIN_MESSAGE_MAP(CDlgGoodOfBill, CDialogEx)
//	ON_WM_NCMOUSEMOVE()
//	ON_WM_MOUSELEAVE()
//	ON_WM_MOUSEHOVER()

ON_NOTIFY(NM_DBLCLK, IDC_LSTGoods, &CDlgGoodOfBill::OnDblclkLstgoods)
END_MESSAGE_MAP()


// CDlgGoodOfBill 消息处理程序




BOOL CDlgGoodOfBill::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	m_list.InsertColumn(0, _T("单价"), LVCFMT_CENTER, 90);
	m_list.InsertColumn(1, _T("重量"), LVCFMT_CENTER, 120);
	m_list.InsertColumn(2, _T("损耗"), LVCFMT_CENTER, 100);
	m_list.InsertColumn(3, _T("总重"), LVCFMT_CENTER, 130);
	m_list.InsertColumn(4, _T("总金额"), LVCFMT_CENTER, 130);
	m_list.InsertColumn(5, _T("备注"), LVCFMT_CENTER, 130);

	DWORD dwStyle = m_list.GetExtendedStyle();
	dwStyle |= LVS_EX_FULLROWSELECT | LVS_EX_GRIDLINES;
	m_list.SetExtendedStyle(dwStyle);
	
	LoadGoodsInList();
	return TRUE;  // return TRUE unless you set the focus to a control
				  // 异常: OCX 属性页应返回 FALSE
}

void CDlgGoodOfBill::LoadGoodsInList()
{
	ADOConn m_conn;
	CString sql;
	sql.Format(_T("SELECT SteelOfBills.ID, SteelOfBills.UnitPrice, SteelOfBills.TotalWeight, SteelOfBills.TotalPrice,SteelOfBills.isWorkLoad, SteelOfBills.Caption, SteelOfBills.Mark, SteelOfBills.NetWeight, SteelOfBills.lossAmout FROM SteelOfBills WHERE(((SteelOfBills.BillId) = %d))"), BID);
	_RecordsetPtr rs = m_conn.GetRecordSet((_bstr_t)sql);
	int index;
	while (!rs->adoEOF)
	{
		UINT GID;
		DOUBLE nitprice, TotalWeight, TotalPrice, NetWeight,lossAmout;
		CString Caption;
		bool isbWorded;
		GID = rs->GetCollect(L"ID");
		nitprice = rs->GetCollect(L"UnitPrice");
		NetWeight = rs->GetCollect(L"NetWeight");
		lossAmout = rs->GetCollect(L"lossAmout");
		TotalWeight = rs->GetCollect(L"TotalWeight");
		TotalPrice = rs->GetCollect(L"TotalPrice");
		isbWorded = rs->GetCollect(L"isWorkLoad");
		Caption = rs->GetCollect(L"Caption");
		Caption += " ";
		Caption += rs->GetCollect(L"Mark");

		struct_UnitPrice* Price = new struct_UnitPrice();
		Price->id = GID;
		Price->UnitPrice = nitprice;
		Price->isWorded = isbWorded;

		m_pPrice.Add(Price);

		index = m_list.GetTopIndex();
		CString temp;

		temp.Format(_T("%0.4lf元"), nitprice);
		LVITEM lvitem;
		lvitem.mask = LVIF_PARAM | LVFIF_TEXT;
		lvitem.iItem = index;
		lvitem.iSubItem = 0;
		lvitem.pszText = (_bstr_t)temp;
		lvitem.lParam = (LPARAM)Price;

		m_list.InsertItem(&lvitem);

		temp.Format(_T("%0.2lf公斤"), NetWeight);
		m_list.SetItemText(index, 1, temp);
		temp.Format(_T("%0.2lf公斤"), lossAmout);
		m_list.SetItemText(index, 2, temp);
		temp.Format(_T("%0.2lf公斤"), TotalWeight);
		m_list.SetItemText(index, 3, temp);

		temp.Format(_T("%0.2lf元"), TotalPrice);
		m_list.SetItemText(index, 4, temp);
		m_list.SetItemText(index, 5, Caption);
		rs->MoveNext();
	}
	rs->Close();
}


void CDlgGoodOfBill::OnDblclkLstgoods(NMHDR *pNMHDR, LRESULT *pResult)
{
	LPNMITEMACTIVATE pNMItemActivate = reinterpret_cast<LPNMITEMACTIVATE>(pNMHDR);
	int nItem = pNMItemActivate->iItem;
	if (nItem >= 0 && nItem < m_list.GetItemCount())
	{
		struct_UnitPrice* price = (struct_UnitPrice*)m_list.GetItemData(nItem);
		UnitPrice = price->UnitPrice;
		bIsWorded = price->isWorded;
		EndDialog(IDOK);
	}

	*pResult = 0;
}
