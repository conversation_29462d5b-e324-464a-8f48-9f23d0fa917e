#include "stdafx.h"
#include "ConfigFile.h"
#include <ShlObj.h>

#define MAX_ALLKEYS 6000		//全部的键名
#define MAX_KEY 260				//一个键名长度
#define MAX_ALLSECTIONS 2048		//全部的段名
#define MAX_SECTION 260				//一个段名长度


CConfigFile::CConfigFile()
{
	m_strFileName = _T("parameter.ini");
	m_strAppdataFile = GetAppDataDir();
}


CConfigFile::~CConfigFile()
{
}

CConfigFile & CConfigFile::instance()
{
	static CConfigFile inst;
	return inst;
}

BOOL CConfigFile::SetFileName(LPCTSTR lpFileName)
{
	CFile file;
	CFileStatus status;
	if (!file.GetStatus(lpFileName, status))
		return TRUE;
	m_strAppdataFile = lpFileName;
	FileExist(lpFileName);
	return FALSE;
}

CString CConfigFile::GetFileName()
{
	return m_strAppdataFile;
}

CString CConfigFile::GetAt(LPCTSTR szSection, LPCTSTR szName, LPCTSTR szDefault) const
{
	return GetIniValue(szSection, szName, szDefault);
}

int CConfigFile::GetAt(LPCTSTR szSection, LPCTSTR szName, int nDefault) const
{
	TCHAR sz[10];
	_itot_s(nDefault, sz, 10);
	const auto result = GetIniValue(szSection, szName, sz);
	return _ttoi(result);
}

double CConfigFile::GetAt(LPCTSTR szSection, LPCTSTR szName, double fDefault) const
{
	CString strDefault;
	strDefault.Format(_T("%G"), fDefault);

	const auto result = GetIniValue(szSection, szName, strDefault);
	return _ttof(result);
}

void CConfigFile::SetAt(LPCTSTR szSection, LPCTSTR szName, LPCTSTR szValue)
{
	SetIniValue(szSection, szName, szValue);
}

void CConfigFile::SetAt(LPCTSTR szSection, LPCTSTR szName, int value)
{
	TCHAR sz[10];
	_itot_s(value, sz, 10);
	SetIniValue(szSection, szName, sz);
}

void CConfigFile::SetAt(LPCTSTR szSection, LPCTSTR szName, double value)
{
	CString strValue;
	strValue.Format(_T("%G"), value);
	SetIniValue(szSection, szName, strValue);
}

BOOL CConfigFile::DelSection(LPCTSTR lpSection)
{
	if (WritePrivateProfileString(lpSection, NULL, NULL, m_strAppdataFile))
		return FALSE;
	else
		return GetLastError();
}

BOOL CConfigFile::DelKey(LPCTSTR lpSection, LPCTSTR lpKey)
{
	if (WritePrivateProfileString(lpSection, lpKey, NULL, m_strAppdataFile))
		return FALSE;
	else
		return GetLastError();
}

CString CConfigFile::GetValue(LPCTSTR lpSection, LPCTSTR lpKey)
{
	DWORD dValue;
	TCHAR lpValue[MAX_PATH] = { 0 };
	dValue = GetPrivateProfileString(lpSection, lpKey, _T(""), lpValue, MAX_PATH, m_strAppdataFile);
	return lpValue;
}

BOOL CConfigFile::SetValue(LPCTSTR lpSection, LPCTSTR lpKey, LPCTSTR lpValue, bool bCreate)
{
	TCHAR lpTemp[MAX_PATH] = { 0 };
	
	//以下if语句表示如果设置bCreate为false时，当没有这个键名时则返回TRUE（表示出错）
	//!*&*none-value*&!* 这是个垃圾字符没有特别意义，这样乱写是防止凑巧相同。
	if (!bCreate)
	{
		GetPrivateProfileString(lpSection, lpKey, _T("!*&*none-value*&!*"), lpTemp, MAX_PATH, m_strAppdataFile);
		if (_tcscmp(lpTemp, _T("!*&*none-value*&!*")) == 0)
			return TRUE;
	}

	if (WritePrivateProfileString(lpSection, lpKey, lpValue, m_strAppdataFile))
		return FALSE;
	else
		return GetLastError();
}

int CConfigFile::GetSections(CStringArray & arrSection)
{
	/*
		本函数基础：
		GetPrivateProfileSectionNames -从ini文件中获得Section的名称
		如果ini中有两个Section:[sec1]和[sec2],则返回的是'sec1',0,'sec2',0,0,当你不知道
		ini中有哪些section的时候可以用这个api来获名称
	*/

	int i;
	int iPos = 0;
	int iMaxCount;
	TCHAR chSectionNames[MAX_ALLSECTIONS] = { 0 };//总的提出来的字符串
	TCHAR chSection[MAX_SECTION] = { 0 };		//存放一个段名
	GetPrivateProfileSectionNames(chSectionNames, MAX_ALLSECTIONS, m_strAppdataFile);

	//以下循环，截断到两个连续的0
	for (i = 0; i < MAX_ALLSECTIONS; i++)
	{
		if (chSectionNames[i] == 0)
			if (chSectionNames[i] == chSectionNames[i + 1])
				break;
	}

	iMaxCount = i + 1;		//要多一个0号元素，即找出全部字符串的结束部分
	arrSection.RemoveAll();	//清空原数组

	for (i = 0; i < iMaxCount; i++)
	{
		chSection[iPos++] = chSectionNames[i];
		if (chSectionNames[i] == 0)
		{
			if (chSection != _T(""))
			{
				arrSection.Add(chSection);
			}
			memset(chSection, 0, MAX_SECTION);
			iPos = 0;
		}
	}
	return (int)arrSection.GetSize();
}

int CConfigFile::GetKeyValues(int & nCount, CStringArray & arrkey, CStringArray & arrValue, LPCTSTR lpSection)
{
	/*
	本函数基础：
	GetPrivateProfileSection-从ini文件中获取一个Section的全部键名及值名
	如果ini中有一个段，其下有“段1=值1" "段2=值2”，则返回的是'段1=值1',0,'段2=值2',0,0,当你不知道
	获得一个段中的所有键及值可以用这个。
	
	*/

	nCount = 0;
	int i = 0;
	int iPos = 0;
	CString strKeyValue;
	int iMaxCount = 0;
	TCHAR chKeyNames[MAX_ALLKEYS] = { 0 };//总的提出来的字符串
	TCHAR chKey[MAX_KEY] = { 0 };		//提出来的一个键名

	GetPrivateProfileSection(lpSection, chKeyNames, MAX_ALLKEYS, m_strAppdataFile);

	for (i = 0; i < MAX_ALLKEYS; i++)
	{
		if (chKeyNames[i] == 0)
			if (chKeyNames[i] == chKeyNames[i + 1])
				break;
	}
	
	iMaxCount = i + 1;		//要多一个0元素，即找出全部字符串的结束部分
	arrkey.RemoveAll();		//清空原数组
	arrValue.RemoveAll();

	for (i = 0; i < iMaxCount; i++)
	{
		chKey[iPos++] = chKeyNames[i];
		if (chKeyNames[i] == 0)
		{
			strKeyValue = chKey;
			CString strKey = strKeyValue.Left(strKeyValue.Find(_T('=')));
			if (strKey != _T(""))
			{
				arrkey.Add(strKeyValue.Left(strKeyValue.Find(_T('='))));
				arrValue.Add(strKeyValue.Mid(strKeyValue.Find(_T('=')) + 1));
			}
			memset(chKey, 0, MAX_KEY);
			iPos = 0;
		}
	}
	return (int)arrkey.GetSize();
}

BOOL CConfigFile::DelAllSections()
{
	int nSection;
	CStringArray arrSection;
	nSection = GetSections(arrSection);
	for (int i = 0; i < nSection; i++)
	{
		if (DelSection(arrSection[i]))
			return GetLastError();
	}
	return FALSE;
}

CString CConfigFile::GetIniValue(LPCTSTR section, LPCTSTR valueName, LPCTSTR sz_default) const
{
	CString value;
	auto dwRs = ::GetPrivateProfileString(section, valueName, sz_default, value.GetBuffer(256), 256, m_strAppdataFile);
	value.ReleaseBuffer();
	if (0x2 != GetLastError())
	{
		//成功
		return value;
	}

	return value = sz_default;
}

void CConfigFile::SetIniValue(LPCTSTR section, LPCTSTR valueName, LPCTSTR value)
{
	::WritePrivateProfileString(section, valueName, value, m_strAppdataFile);
}

bool CConfigFile::IsStartOfOne(LPCTSTR sz, LPCTSTR beg)
{
	if (nullptr == sz || _T('\0') == *sz)
	{
		return false;
	}

	for (auto p = beg; nullptr != p && _T('\0') != *p; ++p)
	{
		if (*sz == *p)
		{
			return true;
		}
	}

	return false;
}

bool CConfigFile::IsEndOfOne(LPCTSTR sz, LPCTSTR end)
{
	if (nullptr == sz || _T('\0') == *sz)
	{
		return false;
	}

	auto p = sz + _tcslen(sz) - 1;
	for (auto pEnd = end; pEnd != nullptr && _T('\0') != *pEnd; pEnd++)
	{
		if (*pEnd == *p)
		{
			return true;
		}
	}
	return false;
}

CString CConfigFile::Combine(LPCTSTR szPath1, LPCTSTR szPath2)
{
	CString result = szPath1;
	bool bPath1EndHas = IsEndOfOne(szPath1, _T("/\\"));
	bool bPath2Start = IsStartOfOne(szPath2, _T("\\/"));
	if (bPath1EndHas&&bPath2Start)
	{
		result += szPath2 + 1;
	}
	else if (!bPath1EndHas && !bPath2Start)
	{
		result += _T('\\');
		result += szPath2;
	}
	else
	{
		result += szPath2;
	}

	return result;
}

CString CConfigFile::GetAppDataDir()
{
	TCHAR szPath[_MAX_PATH];
	::SHGetSpecialFolderPath(nullptr, szPath, CSIDL_APPDATA, TRUE);////得到的是C盘中：C:\Users\<USER>\AppData\Roaming下的目录
	CString strDir = Combine(szPath, _T("\\STEELERP"));
	::CreateDirectory(strDir, nullptr);

	strDir = Combine(strDir, _T("Datas"));
	::CreateDirectory(strDir, nullptr);
	strDir = Combine(strDir, _T("\\") + m_strFileName);
	FileExist(strDir);
	return strDir;
}

BOOL CConfigFile::FileExist(LPCTSTR FileName)
{
	CFile file;
	CFileStatus status;
	if (!file.GetStatus(FileName, status))
	{
		try
		{
			file.Open(FileName, CFile::modeCreate | CFile::modeReadWrite | CFile::modeNoTruncate);
			file.Close();
		}
		catch (CFileException* e)
		{
			e->ReportError();
			e->Delete();
			return FALSE;
		}
		
	}
	return TRUE;
}

