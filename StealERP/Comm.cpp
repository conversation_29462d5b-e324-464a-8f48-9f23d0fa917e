#include "stdafx.h"
#include "Comm.h"

CString GetBigMoney(double dMoney)
{
	CString szChMoney, szNum;
	int iLen, iNum, iAddzero = 0;
	TCHAR* hzUnit[18] = { _T("分"),_T("角"),_T("元"),_T("拾"),_T("佰"),_T("仟"),_T("万"),_T("拾"),_T("佰"),_T("仟"),_T("亿"),_T("拾"),_T("佰"),_T("仟"),_T("万"),_T("拾"),_T("佰"),_T("仟") };
	TCHAR* hzNum[10] = { _T("零"),_T("壹"),_T("贰"),_T("叁"),_T("肆"),_T("伍"),_T("陆"),_T("柒"),_T("捌"),_T("玖") };
	szNum.Format(_T("%18.0f"), dMoney * 100);	//只是到分。
	szNum.TrimLeft();
	iLen = szNum.GetLength();
	if (iLen > 15 || iLen == 0 || dMoney < 0)return _T("");//数据错误返回

	for (int i = 0; i < iLen; i++)
	{
		iNum = _ttoi((LPCTSTR)szNum.Mid(i, 1));
		if (iNum == 0)//如果为零
			iAddzero++;
		else
		{
			if (iAddzero > 0)
				szChMoney += _T("零");
			szChMoney += hzNum[iNum];//转换为相应的数字
			iAddzero = 0;
		}
		if (iNum != 0 || iLen - i == 3 || iLen - i == 11 || ((iLen - i + 1) % 8 == 0 && iAddzero < 4))
			szChMoney += hzUnit[iLen - i - 1]; //添加相应的汉字
	}
	if (szNum.Right(2) == _T("00"))	//汉有角和分
		szChMoney += _T("整");
	return szChMoney;
}
