#include "stdafx.h"
#include "StellOfOrder.h"


bool CStellOfOrder::save(ADOConn & con, int OrderID)
{
	try
	{
		_RecordsetPtr rs = con.GetRecordSet("select top 10 * from SteelOfOrder order by id asc");
		rs->AddNew();
		rs->PutCollect("OrderID", OrderID);
		rs->PutCollect("Caption", (_bstr_t)m_csCaption);
		rs->PutCollect("Price", m_dPrice);
		rs->Update();
	}
	catch (_com_error e)
	{
		AfxMessageBox(e.Description());
		return false;
	}
	return true;
}

bool CStellOfOrder::save(ADOConn & con, int orderID, int nLid)
{
	try
	{
		_RecordsetPtr rs = con.GetRecordSet("select top 10 * from SteelOfOrder order by id asc");
		rs->AddNew();
		rs->PutCollect("OrderID", orderID);
		rs->PutCollect("Caption", (_bstr_t)m_csCaption);
		rs->PutCollect("Price", m_dPrice);
		rs->PutCollect("LID", nLid);
		rs->Update();
	}
	catch (_com_error e)
	{
		AfxMessageBox(e.Description());
		return false;
	}
	return true;
}

double CStellOfOrder::Price()
{
	return m_dPrice;
}

CString CStellOfOrder::Name()
{
	return m_csCaption;
}

CStellOfOrder::CStellOfOrder(double meony, CString csCaption)
{
	goodsType = _T("steel");
	m_csCaption = csCaption;
	m_dPrice = meony;
}


CStellOfOrder::~CStellOfOrder()
{
}
