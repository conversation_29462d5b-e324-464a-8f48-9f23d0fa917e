#include "stdafx.h"
#include "CDetailed.h"


CDetailed::CDetailed(int id,UINT BID,CString name, CString Adress, double weight, CString time, CString marks)
{
	m_ID = id;
	m_BID = BID;
	m_csName = name;
	m_csAdress = Adress;
	m_Weight = weight;
	m_csTime = time;
	m_csMarks = marks;
}

CDetailed::CDetailed()
{
}


CDetailed::~CDetailed()
{
}

UINT CDetailed::BID()
{
	return m_BID;
}

bool CDetailed::save(ADOConn & con,int PID)
{
	bool bTrans = false;
	try
	{
		CString sql;
		sql.Format(_T("Update workload set isPayed=true,PID=%d where ID=%d"), PID, m_ID);
		bTrans = con.ExecuteSQL((_bstr_t)sql);
		
	}
	catch (_com_error e)
	{
		AfxMessageBox(e.Description());
		bTrans = false;
	}
	return bTrans;
}

CString CDetailed::Name()
{
	return m_csName;
}

CString CDetailed::Aress()
{
	return m_csAdress;
}

double CDetailed::Weight()
{
	return m_Weight;
}

CString CDetailed::csTime()
{
	return m_csTime;
}

CString CDetailed::Marks()
{
	return m_csMarks;
}
