#pragma once
#include "afxcmn.h"
#include "ArrearsList.h"
#include "CMoneyEdit.h"
// CDlg_ArrearsList 对话框

class CDlg_ArrearsList : public CDialogEx
{
	DECLARE_DYNAMIC(CDlg_ArrearsList)

public:
	CDlg_ArrearsList(CWnd* pParent = NULL);   // 标准构造函数
	virtual ~CDlg_ArrearsList();

// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_DLG_ArrearsList };
#endif
private:
	CArrearsList m_Arrea;
protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持

	DECLARE_MESSAGE_MAP()
public:
	virtual BOOL OnInitDialog();
	CListCtrl m_ListArr;
	CMoneyEdit m_totalPrice;
	afx_msg void OnBnClickedBtnvalid();
	afx_msg void OnBnClickedBtnprint();
	afx_msg void OnDblclkLstarri(NMHDR *pNMHDR, LRESULT *pResult);
};
