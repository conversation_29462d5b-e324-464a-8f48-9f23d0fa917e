#pragma once
#include "Grain.h"
#include "ConfigFile.h"
// CPreView 对话框



#define PAPER_W			214		//207				//241			//打印纸宽度mm
#define PAPER_H			93.1			//打印纸高度mm
#define PAPER_ONELINE   5			//第一页行数
//#define PAPER_OTHERLINE 5			//其它页行数

typedef struct
{
	int nMaxLine;		//最大行数
	int nCountPage;		//一共页数
	int nCurPage;		//当前页码
	BOOL IsPrint;			//是否打印
	HWND hWnd;				//窗口句柄
	CGrain* grain;

	TCHAR szTag[256];		//其它数据
	int nTag;				//其它数据
	LPVOID lpVoid;			//其它数据
}PRNINFO, *PPRNINFO;


typedef void(*PRINTPREVIEW)(CDC &MemDC, PRNINFO PrnInfo);

// CPreView 对话框

class CPreView : public CDialogEx
{
	DECLARE_DYNAMIC(CPreView)

public:
	CPreView(CWnd* pParent = NULL);   // 标准构造函数
	virtual ~CPreView();

	void SetCallBackFun(PRINTPREVIEW pFun, PRNINFO sPrnInfo);
	void PrintDoc();
	void SetCurrentPage(int, int);
	// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_PREVIEW };
#endif

protected:
	PRINTPREVIEW pDrawInfo;
	PRNINFO PrnInfo;

	CBitmap cBmp;
	CBrush m_brush;
	CPen cPen;
	int nW, nH;
	CRect WndRect;
	int m_CountPage;		//共有多少页
	int m_CurPage;			//当前页


	SCROLLINFO si;
	int iDeltaPerLine, iAccumDelta, xPt, yPt;

	void SetScrollbar(int cx, int cy);

	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持
	DECLARE_MESSAGE_MAP()
public:
	afx_msg void OnPaint();
	virtual BOOL OnInitDialog();
	afx_msg void OnSize(UINT nType, int cx, int cy);
	afx_msg HBRUSH OnCtlColor(CDC* pDC, CWnd* pWnd, UINT nCtlColor);
	virtual BOOL OnCommand(WPARAM wParam, LPARAM lParam);
	virtual BOOL DestroyWindow();
	afx_msg void OnVScroll(UINT nSBCode, UINT nPos, CScrollBar* pScrollBar);
	afx_msg void OnHScroll(UINT nSBCode, UINT nPos, CScrollBar* pScrollBar);
	afx_msg BOOL OnMouseWheel(UINT nFlags, short zDelta, CPoint pt);
	afx_msg BOOL OnEraseBkgnd(CDC* pDC);
};
