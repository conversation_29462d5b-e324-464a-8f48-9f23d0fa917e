#pragma once
#include <wtypes.h>
#include <afxstr.h>
#include <afxcoll.h>

//配置文件 读写ini配置文件

/*
	设置配置文件路径位置在GetAppdataDir()函数中
*/

class CConfigFile
{
public:
	CConfigFile();
	~CConfigFile();
	//配置文件主要分为三部分：字段名，键名，键值
public:
	static CConfigFile& instance();
public:
	BOOL SetFileName(LPCTSTR lpFileName);//设置文件路径
	CString GetFileName();//获取文件路径

	//读取键值
	CString GetAt(LPCTSTR szSection, LPCTSTR szName, LPCTSTR szDefault = nullptr) const;
	int GetAt(LPCTSTR szSection, LPCTSTR szName, int nDefault) const;
	double GetAt(LPCTSTR szSection, LPCTSTR szName, double fDefault) const;
	//写入键值
	void SetAt(LPCTSTR szSection, LPCTSTR szName, LPCTSTR szValue);
	void SetAt(LPCTSTR szSection, LPCTSTR szName, int value);
	void SetAt(LPCTSTR szSection, LPCTSTR szName, double value);

	BOOL DelSection(LPCTSTR lpSection);//删除字段
	BOOL DelKey(LPCTSTR lpSection, LPCTSTR lpKey); //删除某一字段下的键名
	
	CString GetValue(LPCTSTR lpSection, LPCTSTR lpKey);//得到某一字段下某一键名的键值
	BOOL SetValue(LPCTSTR lpSection, LPCTSTR lpKey, LPCTSTR lpValue, bool bCreate = true);//设置键值，bCreate是指段名及键名未存在时，是否创建

	int GetSections(CStringArray& arrSection);//得到全部段名
	int GetKeyValues(int& nCount, CStringArray& arrkey, CStringArray& arrValue, LPCTSTR lpSection);//得到字段下键的总数，键名，和键值
	BOOL DelAllSections();	//删除所有的字段

private:
	CString GetIniValue(LPCTSTR section, LPCTSTR valueName, LPCTSTR sz_default) const;
	void SetIniValue(LPCTSTR section, LPCTSTR valueName, LPCTSTR value);
	bool IsStartOfOne(LPCTSTR sz, LPCTSTR beg);
	bool IsEndOfOne(LPCTSTR sz, LPCTSTR end);
	CString Combine(LPCTSTR szPath1, LPCTSTR szPath2);
	CString GetAppDataDir();//用户信息目录
	BOOL FileExist(LPCTSTR FileName);
private:
	CString m_strAppdataFile;	//放置在appdata目录下
	CString m_strFileName;		//config文件名称
};

#define G_ConfigFile (CConfigFile::instance())