// AddFareDlg.cpp : 实现文件
//

#include "stdafx.h"
#include "StealERP.h"
#include "AddFareDlg.h"
#include "afxdialogex.h"


// AddFareDlg 对话框

IMPLEMENT_DYNAMIC(AddFareDlg, CDialogEx)

AddFareDlg::AddFareDlg(CWnd* pParent /*=NULL*/)
	: CDialogEx(IDD_DLG_FARE, pParent)
{

}

AddFareDlg::~AddFareDlg()
{
}

void AddFareDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_EDT_FARE, m_dFare);
	DDX_Control(pDX, IDC_COMBO1, m_comCaption);
}


BEGIN_MESSAGE_MAP(AddFareDlg, CDialogEx)
	ON_BN_CLICKED(IDOK, &AddFareDlg::OnBnClickedOk)
END_MESSAGE_MAP()


// AddFareDlg 消息处理程序


void AddFareDlg::OnBnClickedOk()
{
	dCost = m_dFare.value;
	//m_comCaption.GetLBText(m_comCaption.GetCurSel(),csCaptiom);
	this->GetDlgItemText(IDC_COMBO1, csCaptiom);
	CDialogEx::OnOK();
}


BOOL AddFareDlg::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	m_comCaption.AddString(_T("车费"));
	m_comCaption.AddString(_T("扎线"));
	m_comCaption.AddString(_T("过磅费"));
	m_comCaption.SetCurSel(0);

	return TRUE;  // return TRUE unless you set the focus to a control
				  // 异常: OCX 属性页应返回 FALSE
}


void AddFareDlg::OnOK()
{
	// TODO: 在此添加专用代码和/或调用基类

	CDialogEx::DestroyWindow();
}


void AddFareDlg::OnCancel()
{
	// TODO: 在此添加专用代码和/或调用基类

	CDialogEx::DestroyWindow();
}


void AddFareDlg::PostNcDestroy()
{
	// TODO: 在此添加专用代码和/或调用基类

	CDialogEx::PostNcDestroy();
	//delete this;
}
