#include "stdafx.h"
#include "Order.h"


COrder::COrder(int customerID,int nLID)
{
	m_CustomerID = customerID;
	m_OrderID = -1;
	LID = nLID;
}


COrder::~COrder()
{
	for (int i = 0; i < m_pGoods.GetSize(); i++)
	{
		delete((CGoodsOfOrder*)m_pGoods[i]);

	}
}

int COrder::addGoods(CGoodsOfOrder * goods)
{
	return m_pGoods.Add(goods);
}

bool COrder::save()
{
	//保存订单信息
	//事务开始
	m_conn.BeginTrans();
	bool bTrans = false;
	try
	{
		_RecordsetPtr rs = m_conn.GetRecordSet("select top 10 * from OrderBills order by id asc");
		rs->AddNew();
		rs->PutCollect("CID", m_CustomerID);		//客户编号
		rs->Update();
		rs->MoveLast();
		_variant_t newID = rs->GetCollect("ID");
		m_OrderID = newID.intVal;

		for (int i = 0; i < m_pGoods.GetSize(); i++)
		{
			bTrans = ((CGoodsOfOrder*)m_pGoods[i])->save(m_conn, m_OrderID,LID);
			if (bTrans == false)
			{
				break;
			}
		}
	}
	catch (_com_error e)
	{
		AfxMessageBox(e.Description());
		bTrans = false;
	}

	if (AfxMessageBox(_T("是否添加？"), MB_YESNO == IDYES) && bTrans == true)
	{
		m_conn.CommitTrans();
	}
	else
	{
		m_conn.RollbackTrans();
		return false;
	}

	m_conn.Close();
	return true;

}

bool COrder::del()
{
	return false;
}
