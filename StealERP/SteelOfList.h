#pragma once
#include "ADOConn.h"

enum SubItemType
{
	WuGouTou = 0,
	<PERSON><PERSON><PERSON><PERSON>Gou=1,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>=2,
	<PERSON><PERSON><PERSON><PERSON><PERSON>=3,
	<PERSON><PERSON><PERSON><PERSON><PERSON>=4,
	<PERSON>g<PERSON>i=5,
	niu<PERSON><PERSON>=6,
	<PERSON><PERSON>=7,
	<PERSON>g<PERSON><PERSON>=8
};



class CSteelOfList
{
private:
	int m_nLiSHu;				//里数
	double m_dLength;			//长度
	int m_ShuLiang;			//数量
	SubItemType m_SubType;		//弯头类型
	bool m_bBaoGou;				//包勾头为1
public:
	CSteelOfList(int nLiShu,double length,int shuliang,SubItemType type,bool bBaoGou);
	
	~CSteelOfList();
	double Weight;
	CString csWanTou;
	bool save(ADOConn& con, int listID);
};

