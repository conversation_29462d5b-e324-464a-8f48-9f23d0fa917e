#pragma once
#include "afxwin.h"
#include "Receipt.h"
#include "PriceEdit.h"


// CDlgCollections 对话框

class CDlgCollections : public CDialogEx
{
	DECLARE_DYNAMIC(CDlgCollections)

public:
	CDlgCollections(CWnd* pParent = NULL);   // 标准构造函数
	CDlgCollections(UINT UID = NULL, CWnd* pParent = NULL);
	virtual ~CDlgCollections();

// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_DLG_Collections };
#endif

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持

	DECLARE_MESSAGE_MAP()
public:
	int UID;
	afx_msg void OnBnClickedOk();
	CStatic m_staCustomerName;
	afx_msg HBRUSH OnCtlColor(CDC* pDC, CWnd* pWnd, UINT nCtlColor);
	CStatic m_staHaiQian;
	CStatic m_staJRLK;
	CStatic m_staXQJE;
private:
	CFont m_font;
	CFont m_font2;
	CReceipt m_Receipt;
	double m_totalAmount;
	double m_Price;
	double m_HQJE;
public:
	virtual BOOL OnInitDialog();
	CComboBox m_cboMedthod;
	CMoneyEdit m_EDTPrice;
	afx_msg void OnChangeEdtPrice();
};
