#pragma once
#include "PriceEdit.h"
#include "afxwin.h"


// AddFareDlg 对话框

class AddFareDlg : public CDialogEx
{
	DECLARE_DYNAMIC(AddFareDlg)

public:
	AddFareDlg(CWnd* pParent = NULL);   // 标准构造函数
	virtual ~AddFareDlg();

// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_DLG_FARE };
#endif

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持

	DECLARE_MESSAGE_MAP()
private:
	CComboBox m_comCaption;
	CMoneyEdit m_dFare;
public:
	double dCost;
	CString csCaptiom;
	afx_msg void OnBnClickedOk();
	virtual BOOL OnInitDialog();
	
	virtual void OnOK();
	virtual void OnCancel();
	virtual void PostNcDestroy();
};
