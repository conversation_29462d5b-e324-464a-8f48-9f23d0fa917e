// SortEdit.cpp : 实现文件
//

#include "stdafx.h"
#include "StealERP.h"
#include "SortEdit.h"
#include "afxdialogex.h"



// CSortEdit 对话框

IMPLEMENT_DYNAMIC(CSortEdit, CDialogEx)

CSortEdit::CSortEdit(CWnd* pParent /*=NULL*/)
	: CDialogEx(IDD_DIALOG1, pParent)
	, m_GName(_T(""))
	, m_PName(_T(""))
{

}

CSortEdit::~CSortEdit()
{
}

void CSortEdit::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Text(pDX, IDC_AreaName_ED, m_GName);
	DDX_Text(pDX, IDC_PID_STATIC, m_PName);
}


BEGIN_MESSAGE_MAP(CSortEdit, CDialogEx)
	ON_STN_CLICKED(IDC_PID_STATIC, &CSortEdit::OnStnClickedPidStatic)

	ON_BN_CLICKED(IDOK, &CSortEdit::OnBnClickedOk)
END_MESSAGE_MAP()


// CSortEdit 消息处理程序


void CSortEdit::OnStnClickedPidStatic()
{
	// TODO: 在此添加控件通知处理程序代码
}






void CSortEdit::OnBnClickedOk()
{
	UpdateData(TRUE);
	CDialogEx::OnOK();
}
