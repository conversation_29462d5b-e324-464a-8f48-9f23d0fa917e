#include "stdafx.h"
#include "SteelOfBill.h"


CSteelOfBill::CSteelOfBill(double NetWeight, double UnitPrice,CString Caption,CString Mark,bool ifLoss, bool ifPayWork)
{
	m_csGuiGe = CString("钢材");
	m_csDanWei = CString("KG");

	goodsType = _T("steel");
	m_NetWeight = NetWeight;
	m_UnitPrice = UnitPrice;
	m_csCaption = Caption;
	m_csMark = Mark;
	m_ifLoss = ifLoss;
	m_ifWorkPay = ifPayWork;
	if (m_ifLoss == true)
	{
		m_lossAmout = m_NetWeight * 0.03;
	}
	else
		m_lossAmout = 0;
	m_TotalWeight = m_NetWeight + m_lossAmout;
	m_TotalPrice = m_TotalWeight * m_UnitPrice;
}

CSteelOfBill::CSteelOfBill(UINT bid, double netWeight, double UnitPrice, bool ifLoos, double lossAmout, double TotalWeight, CString time, double TotalPrice, CString Caption, CString Mark, bool isWorLoad)
{
	Billid = bid;
	m_csGuiGe = CString("钢材");
	m_csDanWei = CString("公斤");
	AddTime = time;
	goodsType = _T("steel");
	m_NetWeight = netWeight;
	m_UnitPrice = UnitPrice;
	m_csCaption = Caption;
	m_csMark = Mark;
	m_ifLoss = ifLoos;
	m_ifWorkPay = isWorLoad;
	m_lossAmout = lossAmout;
	m_TotalWeight = TotalWeight;
	m_TotalPrice = TotalPrice;
}

CSteelOfBill::~CSteelOfBill()
{
}

bool CSteelOfBill::save(ADOConn& con, int billID)
{
	try
	{
		int nLoss = m_ifLoss == true ? 1 : 0;
		_RecordsetPtr rs = con.GetRecordSet("select top 10 * from SteelOfBills order by id asc");
		rs->AddNew();
		rs->PutCollect("BillID", billID);					//单据编号
		rs->PutCollect("NetWeight", m_NetWeight);			//数量公斤，不包括损耗
		rs->PutCollect("UnitPrice", m_UnitPrice);			//单价
		rs->PutCollect("Caption", (_bstr_t)m_csCaption);	//名称
		rs->PutCollect("Mark", (_bstr_t)m_csMark);			//备注
		rs->PutCollect("TotalPrice", m_TotalPrice);			//总价
		rs->PutCollect("ifLoss", nLoss);					//是否讲算损耗
		rs->PutCollect("lossAmout", m_lossAmout);			//损耗重量
		rs->PutCollect("TotalWeight", m_TotalWeight);		//总重量，包括损耗。
		rs->PutCollect("isWorkLoad", m_ifWorkPay);			//是否为工人制造
		rs->Update();

		//添加工人制作记录
		if (m_ifWorkPay == true)
		{
			rs = con.GetRecordSet("select top 1 * from workload order by id asc");
			rs->AddNew();
			rs->PutCollect("BillID", billID);
			rs->PutCollect("Weight", m_NetWeight);//注意这里不包括损耗
			rs->Update();
		}
	}
	catch (_com_error e)
	{
		AfxMessageBox(e.Description());
		return false;
	}
	return true;
}

double CSteelOfBill::totalPrice()
{
	return m_TotalPrice;
}

CString CSteelOfBill::Name()
{
	return m_csCaption;
}

CString CSteelOfBill::GuiGe()
{
	CString temp;
	temp.Format(_T("%0.1lfkg"), m_NetWeight);
	return temp;
}

CString CSteelOfBill::DanWei()
{
	CString temp;
	temp.Format(_T("%0.1lfkg"), m_lossAmout);
	return temp;
}

double CSteelOfBill::ShuLian()
{
	return m_TotalWeight;
}

double CSteelOfBill::DanJia()
{
	return m_UnitPrice;
}

CString CSteelOfBill::BeiZhu()
{
	return m_csMark;
}

CString CSteelOfBill::addTime()
{
	return AddTime;
}

UINT CSteelOfBill::billid()
{
	return Billid;
}

double CSteelOfBill::LossWeight()
{
	return m_lossAmout;
}

double CSteelOfBill::Weight()
{
	return m_NetWeight;
}
