<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="StealERP.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="StealERPDlg.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="stdafx.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="targetver.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="BingLingDlg.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="CustomerDlg.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="CustSort.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="SortEdit.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ADOConn.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="AddCustDlg.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="CustManager.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="CSearchCustEdit.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="CSearchList.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="CSearchDlg.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ComboBoxIs.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="CCustomer.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Comm.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ExpressBillDLG.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ExpAddCustDlg.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ComboBoxArea.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="CMoneyEdit.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="CWeightEdit.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="PriceEdit.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Grain.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Bill.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Goods.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Steels.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Tile.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="OtherGoods.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="GoodsOfBill.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="SteelsOfBill.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="TileOfBill.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="OtherGoodsOfBill.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="SteelOfBill.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="SellGoodsDlg.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="PreParent.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="PreView.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="OrderDlg.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Order.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="GoodsOfOrder.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="StellOfOrder.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="AddFareDlg.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="COtherOfBill.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="DlgReckon.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="easysize.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="SteelList.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="SteelOfList.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="DlgBaoGouTiWen.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ConfigFile.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="CustList.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ListButton.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="DlgCollections.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Receipt.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="DlgCollList.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ShouKuanLei.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="DlgReturn.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="CustomerOfReturn.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="RBill.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="DlgGoodOfBill.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="DlgDataMing.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="DlgPayedWage.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Wage.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="CDetailed.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Payroll.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="WeightInTonEdit.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="DlgPhone.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Dlg_ArrearsList.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ArrearsList.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Arrears.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ExcelDoc.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Lock.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Log.h">
      <Filter>头文件</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="StealERP.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="StealERPDlg.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="stdafx.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="BingLingDlg.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="CustomerDlg.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="CustSort.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="SortEdit.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="ADOConn.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="AddCustDlg.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="CustManager.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="CSearchCustEdit.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="CSearchList.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="CSearchDlg.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="ComboBoxIs.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="CCustomer.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="ExpressBillDLG.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="ExpAddCustDlg.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="ComboBoxArea.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="CMoneyEdit.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="CWeightEdit.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="PriceEdit.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Grain.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Bill.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Goods.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Steels.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Tile.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="OtherGoods.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="GoodsOfBill.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="SteelsOfBill.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="TileOfBill.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="OtherGoodsOfBill.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="SteelOfBill.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="SellGoodsDlg.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="PreParent.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="PreView.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Comm.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="OrderDlg.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Order.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="GoodsOfOrder.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="StellOfOrder.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="AddFareDlg.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="COtherOfBill.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="DlgReckon.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="SteelList.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="SteelOfList.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="DlgBaoGouTiWen.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="ConfigFile.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="CustList.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="ListButton.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="DlgCollections.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Receipt.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="DlgCollList.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="ShouKuanLei.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="DlgReturn.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="CustomerOfReturn.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="RBill.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="DlgGoodOfBill.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="DlgDataMing.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="DlgPayedWage.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Wage.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="CDetailed.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Payroll.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="WeightInTonEdit.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="DlgPhone.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Dlg_ArrearsList.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="ArrearsList.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Arrears.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="ExcelDoc.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Log.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="StealERP.rc">
      <Filter>资源文件</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="res\StealERP.rc2">
      <Filter>资源文件</Filter>
    </None>
    <None Include="resource.hm">
      <Filter>头文件</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Image Include="res\StealERP.ico">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\Toolbar.bmp">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\Toolbar256.bmp">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\icon1.ico">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="258.png">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="..\..\..\迅雷下载\20180225105806185_easyicon_net_64.ico">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="..\..\..\迅雷下载\20180225110149515_easyicon_net_64.ico">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="..\..\..\迅雷下载\bitmap1.bmp">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="C:\Users\<USER>\Pictures\微信截图_20180308222208.bmp">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\bmp_back.bmp">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\toolbar1.bmp">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\bitbug_favicon(4).ico">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\bitbug_favicon(5).ico">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\bitbug_favicon(6).ico">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\cancel.ico">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\first.ico">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\last.ico">
      <Filter>资源文件</Filter>
    </Image>
  </ItemGroup>
</Project>