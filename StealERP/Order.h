#pragma once
#include "Grain.h"
#include "ADOConn.h"
#include "GoodsOfOrder.h"

class COrder :
	public CGrain
{
private:
	int m_OrderID;			//订单数据编号
	int m_CustomerID;			//客户ID
public:
	int LID;					//铁数ID
public:
	ADOConn m_conn;
	CPtrArray m_pGoods;				//商品组
	COrder(int customerID, int LID = 0);
	~COrder();
	int addGoods(CGoodsOfOrder* goods);			//添加商品
	bool save();			//保存单据
	bool del();				//从数据库中删除订单数据。

};

