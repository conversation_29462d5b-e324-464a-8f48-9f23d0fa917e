// ExpressBillDLG.cpp : 实现文件
//

#include "stdafx.h"
#include "StealERP.h"
#include "ExpressBillDLG.h"
#include "afxdialogex.h"
#include "ExpAddCustDlg.h"
#include "CCustomer.h"
#include "AddFareDlg.h"
#include "COtherOfBill.h"
#include "DlgReckon.h"
#include "CustManager.h"

// CExpressBillDLG 对话框

IMPLEMENT_DYNAMIC(CExpressBillDLG, CDialogEx)



CExpressBillDLG::CExpressBillDLG(CWnd* pParent /*=NULL*/)
	: CDialogEx(IDD_DLG_ExpressBill, pParent)
{
	CustomerID = -1;
	m_dWeight = 0.0;
	m_dTotalWeight = 0.0;
	m_dLossWeight = 0.0;
	m_dPrice = 0.0;
	//  nGridItem = 0;
	itemNo = 0;			//商品序列，定位用。
	nRetSteelListDataID = -1;
	OrderId = -1;
	m_bParent = FALSE;


}

CExpressBillDLG::CExpressBillDLG(UINT OID)
{
	CustomerID = -1;
	m_dWeight = 0.0;
	m_dTotalWeight = 0.0;
	m_dLossWeight = 0.0;
	m_dPrice = 0.0;
	//  nGridItem = 0;
	itemNo = 0;			//商品序列，定位用。
	nRetSteelListDataID = -1;
	OrderId = OID;
	m_bParent = FALSE;
}

CExpressBillDLG::CExpressBillDLG(CString s, int userid)
{
	CustomerID = userid;
	m_dWeight = 0.0;
	m_dTotalWeight = 0.0;
	m_dLossWeight = 0.0;
	m_dPrice = 0.0;
	//  nGridItem = 0;
	itemNo = 0;			//商品序列，定位用。
	nRetSteelListDataID = -1;
	OrderId = -1;
	if (s == _T("TRUE"))
		m_bParent = TRUE;
}



CExpressBillDLG::~CExpressBillDLG()
{
	if (model != NULL)
	{
		delete model;
		model = NULL;
	}
}




void CExpressBillDLG::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_CHCK_LOSS, m_ifLoss);
	DDX_Control(pDX, IDC_CB_CUST, m_cbCustomer);
	DDX_Control(pDX, IDC_EDT_WEIGHT, m_EdtWeight);
	DDX_Control(pDX, IDC_EDT_PRICE, m_EDTPrice);
	DDX_Control(pDX, IDC_EDT_AMOUNT, m_EdtMount);
	DDX_Control(pDX, IDC_PAY_CHCK, m_WorkPay);
	DDX_Control(pDX, IDC_CMBCaption, m_comGoodsCaption);
	DDX_Control(pDX, IDC_EDT_MARK, m_EdtMark);
	DDX_Control(pDX, IDC_GOODS_LST, m_GoodsList);
	DDX_Control(pDX, IDC_EDT_TotalWeight, m_TotalWeight);
	DDX_Control(pDX, IDC_BTN_DEL, m_Btn_Del);
	DDX_Control(pDX, IDC_BTN_ADD, m_BtnAdd);
}

LRESULT CExpressBillDLG::OnWeightChange(WPARAM wParam, LPARAM lParam)
{
	m_dWeight = m_EdtWeight.value;
	return WeightChange();

}

LRESULT CExpressBillDLG::OnPriceChange(WPARAM wParam, LPARAM lParam)
{
	m_dPrice = m_EDTPrice.value;
	return 0;
}

LRESULT CExpressBillDLG::OnReckonMount(WPARAM wParam, LPARAM lParam)
{
	double totalmount = m_dTotalWeight * m_dPrice;
	CString s;
	s.Format(_T("¥%0.2lf元"), totalmount);
	m_EdtMount.SetWindowText(s);
	return 0;
}


BEGIN_MESSAGE_MAP(CExpressBillDLG, CDialogEx)
	ON_MESSAGE(WM_WEIGHT_EVENT, OnWeightChange)
	ON_MESSAGE(WM_PRICECHANGE_EVENT, OnPriceChange)
	ON_MESSAGE(WM_RECKONMOUNT_EVENT, OnReckonMount)
	ON_BN_CLICKED(IDC_BUTTON1, &CExpressBillDLG::OnBnClickedButton1)
	ON_BN_CLICKED(IDC_CHCK_LOSS, &CExpressBillDLG::OnClickedChckLoss)
	ON_BN_CLICKED(ID_KAIPIAO, &CExpressBillDLG::OnBnClickedKaipiao)
	ON_CBN_SELCHANGE(IDC_CMBCaption, &CExpressBillDLG::OnSelchangeCmbcaption)
	ON_BN_CLICKED(IDC_BTN_ADD, &CExpressBillDLG::OnBnClickedBtnAdd)
	ON_BN_CLICKED(IDC_BTN_DEL, &CExpressBillDLG::OnBnClickedBtnDel)
	ON_NOTIFY(LVN_INSERTITEM, IDC_GOODS_LST, &CExpressBillDLG::OnInsertitemGoodsLst)
	ON_NOTIFY(LVN_DELETEITEM, IDC_GOODS_LST, &CExpressBillDLG::OnDeleteitemGoodsLst)
	ON_BN_CLICKED(IDC_BTN_Com, &CExpressBillDLG::OnBnClickedBtnCom)
	ON_WM_SIZE()
	ON_BN_CLICKED(IDC_BTNConfirm, &CExpressBillDLG::OnBnClickedBtnconfirm)
END_MESSAGE_MAP()


// CExpressBillDLG 消息处理程序


BOOL CExpressBillDLG::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	// TODO:  在此添加额外的初始化
	m_ifLoss.SetCheck(BST_CHECKED);


	m_cbCustomer.m_pItemDlbFun = OnComboBoxDb;
	m_cbCustomer.m_pItemReturn = OnComboBoxReturn;

	m_EdtWeight.SetWindowText(_T("0.0公斤"));

	m_comGoodsCaption.AddString(_T("钢材"));
	m_comGoodsCaption.AddString(_T("其它"));
	m_comGoodsCaption.SetCurSel(0);



	//	m_GoodsList.InsertColumn(0, _T("序号"), LVCFMT_LEFT, 50);
	m_GoodsList.InsertColumn(0, _T("商品类型"), LVCFMT_LEFT, 100);
	m_GoodsList.InsertColumn(1, _T("数量(重量)"), LVCFMT_LEFT, 100);
	m_GoodsList.InsertColumn(2, _T("单价"), LVCFMT_LEFT, 60);
	m_GoodsList.InsertColumn(3, _T("损耗"), LVCFMT_LEFT, 60);
	m_GoodsList.InsertColumn(4, _T("工人做"), LVCFMT_CENTER, 60);
	m_GoodsList.InsertColumn(5, _T("总金额"), LVCFMT_LEFT, 100);

	DWORD dwStyle = m_GoodsList.GetExtendedStyle();
	dwStyle |= LVS_EX_FULLROWSELECT | LVS_EX_GRIDLINES;
	m_GoodsList.SetExtendedStyle(dwStyle);

	if (OrderId > -1)
	{
		model = new CBill(CustomerID,SELL,OrderId);
		CustomerID = model->CustomerID;
		nRetSteelListDataID = model->LID;
		PushPriceAndWeight();
		m_cbCustomer.PushName(CustomerID);
	}
	else
	{
		if (CustomerID > -1)
		{
			model = new CBill(CustomerID, SELL);
			m_cbCustomer.PushName(CustomerID);
		}
		else
			model = new CBill(CustomerID, SELL);
	}
		


	m_BtnAdd.EnableWindow(FALSE);
	return TRUE;  // return TRUE unless you set the focus to a control
				  // 异常: OCX 属性页应返回 FALSE
}


void CExpressBillDLG::OnBnClickedButton1()
{
	CExpAddCustDlg dlg;
	if (dlg.DoModal() == IDOK)
	{
		CustomerID = dlg.newUSerID;
		CCustomer customer(CustomerID);
		CString temp;
		temp.Format(_T("%s【%s】"), customer.Name, customer.Adress);
		m_cbCustomer.Clear();
		int iAdd = m_cbCustomer.AddString(temp);
		m_cbCustomer.SetItemData(iAdd, CustomerID);
		m_cbCustomer.SelectString(0, temp);
	}
}

void CExpressBillDLG::OnComboBoxDb(HWND * hWnd, int nIndex)
{
	CExpressBillDLG* dlg = (CExpressBillDLG*)hWnd;
	dlg->CustomerID = nIndex;
	dlg->model->setCustId(dlg->CustomerID);
}

void CExpressBillDLG::OnComboBoxReturn(HWND * hWnd)
{
}








void CExpressBillDLG::OnClickedChckLoss()
{
	SendMessage(WM_WEIGHT_EVENT, 0, 0);
	SendMessage(WM_RECKONMOUNT_EVENT, 0, 0);
}

//保存开票。
void CExpressBillDLG::OnBnClickedKaipiao()
{

	// TODO: 在此添加控件通知处理程序代码
	if (model->m_pGoods.GetSize() > 0)
	{
		if (model->save())
		{
			model->PrintDoc(this->m_hWnd);
			if (m_bParent == FALSE)
			{
				CCustManager* pdlg = new CCustManager(CustomerID);
				pdlg->Create(IDD_CUSTMANAGE_DLG);
				pdlg->ShowWindow(SW_SHOW);
				pdlg = NULL;//*/
			}
			
		}
	}

	/*delete steel;
	steel = NULL;*/
	CDialogEx::OnOK();
}


void CExpressBillDLG::OnOK()
{
	// TODO: 在此添加专用代码和/或调用基类
	CDialogEx::DestroyWindow();
	//CDialogEx::OnOK();
}


BOOL CExpressBillDLG::PreTranslateMessage(MSG* pMsg)
{
	ASSERT(pMsg != NULL);
	if (pMsg->message == WM_KEYDOWN && pMsg->wParam == VK_ESCAPE)
	{
		pMsg->wParam = VK_RETURN;
	}

	return CDialogEx::PreTranslateMessage(pMsg);
}


void CExpressBillDLG::OnSelchangeCmbcaption()
{
	int sel = m_comGoodsCaption.GetCurSel();
	if (sel == 1)
	{
		AddFareDlg dlg;
		if (dlg.DoModal() == IDOK)
		{

			if (CustomerID < 0)
			{
				AfxMessageBox(_T("请选择正确的用户！"));
				m_cbCustomer.SetFocus();
				m_comGoodsCaption.SetCurSel(0);
				return;
			}

			int index = m_GoodsList.GetTopIndex();
			CString Caption, temp;
			double Cost;
			Cost = dlg.dCost;
			Caption = dlg.csCaptiom;

			CGoodsOfBill* OtherCost = new COtherOfBill(Caption, Cost);
			model->addGoods(OtherCost);

			LVITEM lvitem;
			lvitem.mask= LVIF_PARAM | LVFIF_TEXT;
			lvitem.iItem = index;
			lvitem.iSubItem = 0;
			lvitem.pszText = (_bstr_t)Caption;
			lvitem.lParam = (LPARAM)OtherCost;

			m_GoodsList.InsertItem(&lvitem);

			m_GoodsList.SetItemText(index, 1, _T("-"));
			temp.Format(_T("%0.2lf元"), Cost);
			m_GoodsList.SetItemText(index, 2, temp);
			m_GoodsList.SetItemText(index, 3, _T("-"));
			m_GoodsList.SetItemText(index, 4, _T("-"));
			m_GoodsList.SetItemText(index, 5, temp);
		}

		m_comGoodsCaption.SetCurSel(0);
		m_EDTPrice.SetFocus();
	}
}


void CExpressBillDLG::OnBnClickedBtnAdd()
{
	if (CustomerID < 0)
	{
		AfxMessageBox(_T("请选择正确的用户！"));
		m_cbCustomer.SetFocus();
		return;
	}
	int ifLoss = m_ifLoss.GetCheck();
	int ifPayWork = m_WorkPay.GetCheck();
	m_dPrice = m_EDTPrice.value;
	CString csGoodsCaption, csMark;
	m_comGoodsCaption.GetWindowText(csGoodsCaption);
	m_EdtMark.GetWindowText(csMark);



	CGoodsOfBill* steel = new CSteelOfBill(m_dWeight, m_dPrice, csGoodsCaption, csMark, (bool)ifLoss, (bool)ifPayWork);
	model->addGoods(steel);

	int index = m_GoodsList.GetTopIndex();

	CString temp;

	LVITEM lvitem;
	lvitem.mask = LVIF_PARAM | LVFIF_TEXT;
	lvitem.iItem = index;
	lvitem.iSubItem = 0;
	lvitem.pszText = (_bstr_t)csGoodsCaption;
	lvitem.lParam = (LPARAM)steel;

	m_GoodsList.InsertItem(&lvitem);
	temp.Format(_T("%0.1lfKG"), m_dWeight);
	m_GoodsList.SetItemText(index, 1, temp);
	temp.Format(_T("%0.3lf元"), m_dPrice);
	m_GoodsList.SetItemText(index, 2, temp);
	temp.Format(_T("%0.1lfKG"), m_dLossWeight);
	m_GoodsList.SetItemText(index, 3, temp);
	ifPayWork == 1 ? temp.Format(_T("√")) : temp.Format(_T("×"));
	m_GoodsList.SetItemText(index, 4, temp);

	temp.Format(_T("%0.2lf元"), m_dTotalWeight*m_dPrice);

	m_GoodsList.SetItemText(index, 5, temp);


	Clear();
	//	AddGoodsList(steel);
}

void CExpressBillDLG::ChangeSize(CWnd * pWnd, int cx, int cy)
{
	if (pWnd)
	{
		CRect rect;
		pWnd->GetWindowRect(&rect);
		ScreenToClient(&rect);

		rect.left = rect.left*cx / m_rect.Width();
		rect.right = rect.right*cx / m_rect.Width();
		rect.top = rect.top*cy / m_rect.Height();
		rect.bottom = rect.bottom*cy / m_rect.Height();
		pWnd->MoveWindow(rect);
	}
}

//添加后清空edit控件数
void CExpressBillDLG::Clear()
{
	m_WorkPay.SetCheck(false);
	m_EDTPrice.ClearALL();
	m_EdtWeight.ClearALL();
	m_TotalWeight.SetSel(0, -1);
	m_TotalWeight.ReplaceSel(_T(""));
	m_EdtMount.SetSel(0, -1);
	m_EdtMount.ReplaceSel(_T(""));
	m_EdtMark.SetSel(0, -1);
	m_EdtMark.ReplaceSel(_T(""));
	m_EDTPrice.SetFocus();
	m_BtnAdd.EnableWindow(false);
}

void CExpressBillDLG::PushPriceAndWeight()
{
	ADOConn m_conn;
	CString sql;
	sql.Format(_T("SELECT sum(steelform.dWeight) as Weight, SteelOfOrder.Price FROM (SteelOfOrder left JOIN List_outfit on SteelOfOrder.LID = List_outfit.ID ) left JOIN steelform ON steelform.OID = List_outfit.ID where SteelOfOrder.orderID = %d group by SteelOfOrder.Price"), OrderId);
	_RecordsetPtr rs = m_conn.GetRecordSet((_bstr_t)sql);
	while (!rs->adoEOF)
	{
		_variant_t temp = rs->GetCollect(L"Price");
		if (temp.vt != VT_EMPTY && temp.vt != VT_NULL)
			m_dPrice = temp;
		temp = rs->GetCollect(L"Weight");
		if (temp.vt != VT_EMPTY && temp.vt != VT_NULL)
			m_dWeight = temp;
		rs->MoveNext();
	}
	rs->Close();
	m_EDTPrice.value = m_dPrice;
	double f;
	if (m_dPrice > 1000)
		f = m_dPrice / 1000;
	else
		f = m_dPrice;
	if (f < 10)
		f *= 1000;
	CString s;
	s.Format(_T("¥%0.2lf元/吨"), f);
	m_EDTPrice.SetWindowText(s);
	m_EdtWeight.value = m_dWeight;
	s.Format(_T("%0.1lf公斤"), m_dWeight);
	m_EdtWeight.SetWindowText(s);
	SendMessage(WM_WEIGHT_EVENT, 0, 0);
	SendMessage(WM_RECKONMOUNT_EVENT, 0, 0);
	//WeightChange();
}



void CExpressBillDLG::AddGoodsList(CGoodsOfBill* goods)
{

	//m_GoodsList.InsertColumn(0,1, )
}

//删除键
void CExpressBillDLG::OnBnClickedBtnDel()
{
	if (m_GoodsList.GetSelectedCount()) {
		while (m_GoodsList.GetNextItem(-1, LVNI_ALL | LVNI_SELECTED) != -1)
		{
			int nItem = m_GoodsList.GetNextItem(-1, LVNI_ALL | LVNI_SELECTED);
			CGoodsOfBill* Getdel = (CGoodsOfBill*)m_GoodsList.GetItemData(nItem);
			m_GoodsList.DeleteItem(nItem);
			//删除
			model->DelItem(Getdel);
		}
	}
}


void CExpressBillDLG::OnInsertitemGoodsLst(NMHDR *pNMHDR, LRESULT *pResult)
{
	LPNMLISTVIEW pNMLV = reinterpret_cast<LPNMLISTVIEW>(pNMHDR);
	if (m_cbCustomer.IsWindowEnabled())
		m_cbCustomer.EnableWindow(FALSE);
	*pResult = 0;
}


void CExpressBillDLG::OnDeleteitemGoodsLst(NMHDR *pNMHDR, LRESULT *pResult)
{
	LPNMLISTVIEW pNMLV = reinterpret_cast<LPNMLISTVIEW>(pNMHDR);
	int i = m_GoodsList.GetItemCount();
	if (i == 1 && !(m_cbCustomer.IsWindowEnabled()))
	{
		m_cbCustomer.EnableWindow(TRUE);
	}
	*pResult = 0;
}


void CExpressBillDLG::OnBnClickedBtnCom()
{
	CDlgReckon dlg;
	if (nRetSteelListDataID > 0)
		dlg.SteelDateID = nRetSteelListDataID;
	CString temp;
	m_cbCustomer.GetWindowText(dlg.CustomerName);
	if (dlg.DoModal() == IDOK)
	{
		nRetSteelListDataID = dlg.SteelDateID;
		model->LID = nRetSteelListDataID;
		m_dWeight = dlg.dAllWeight;
		CString s;
		s.Format(_T("%0.2lf"), m_dWeight);
		m_EdtWeight.SetWindowText(s);
		::SendMessage(m_EdtWeight.m_hWnd, WM_KILLFOCUS, -1, 0);


	}
	
}


void CExpressBillDLG::OnSize(UINT nType, int cx, int cy)
{
	CDialogEx::OnSize(nType, cx, cy);

	if (nType == 1)return;
	CWnd* pWnd;
	pWnd = GetWindow(GW_CHILD);

	while (pWnd != NULL)
	{
		ChangeSize(pWnd, cx, cy);
		pWnd = pWnd->GetNextWindow();
	}
	GetClientRect(&m_rect);
}

LRESULT CExpressBillDLG::WeightChange()
{
	
	if (m_ifLoss.GetCheck())
		m_dLossWeight = m_dWeight * 0.03;
	else
		m_dLossWeight = 0;
	m_dTotalWeight = m_dWeight + m_dLossWeight;
	CString s;
	s.Format(_T("%0.1lf公斤"), m_dTotalWeight);
	m_TotalWeight.SetWindowText(s);
	return 0;
}


void CExpressBillDLG::PostNcDestroy()
{
	// TODO: 在此添加专用代码和/或调用基类

	CDialogEx::PostNcDestroy();
	delete this;
}


void CExpressBillDLG::OnCancel()
{
	// TODO: 在此添加专用代码和/或调用基类
	CDialogEx::DestroyWindow();
	//CDialogEx::OnCancel();
}


void CExpressBillDLG::OnBnClickedBtnconfirm()
{
	if (CustomerID > 0 && m_EDTPrice.value > 0 && m_EdtWeight.value > 0)
	{
		m_BtnAdd.EnableWindow(true);
	}
	else
		AfxMessageBox(_T("请先填写单据信息"));
}


