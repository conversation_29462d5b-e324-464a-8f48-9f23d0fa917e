#pragma once
#include "Grain.h"
#include "Goods.h"
#include "ADOConn.h"

class CGoodsOfBill :
	public CGrain
{
public:
	CString goodsType;
protected:
	double m_TotalPrice;			//该商品总价。
public:
	virtual bool save(ADOConn& con,int billID) = 0;	//保存
	virtual double totalPrice() = 0;	//商品金额
	virtual CString Name() = 0;		//商品全名
	virtual CString GuiGe();		//规格
	virtual CString DanWei();		//单位
	virtual double ShuLian() = 0;	//数量
	virtual double DanJia() = 0;	//单价
	virtual CString BeiZhu() = 0;		//备注
	virtual CString addTime() = 0;		//添加时间
	virtual UINT billid() = 0;			//单据iD
	CGoodsOfBill();
	~CGoodsOfBill();
};

