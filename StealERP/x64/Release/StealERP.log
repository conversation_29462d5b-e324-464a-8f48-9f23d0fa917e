  stdafx.cpp
  AddCustDlg.cpp
  AddFareDlg.cpp
  ADOConn.cpp
  Arrears.cpp
  ArrearsList.cpp
  Bill.cpp
Bill.cpp(89): warning C4244: “return”: 从“INT_PTR”转换到“int”，可能丢失数据
Bill.cpp(210): warning C4244: “=”: 从“INT_PTR”转换到“int”，可能丢失数据
Bill.cpp(230): warning C4018: “<”: 有符号/无符号不匹配
Bill.cpp(311): warning C4244: “初始化”: 从“INT_PTR”转换到“int”，可能丢失数据
Bill.cpp(469): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(476): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(487): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(495): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(497): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(498): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(499): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(500): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(501): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(502): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(503): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(541): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(542): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(543): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(544): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(545): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(546): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(547): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(548): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(549): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(552): warning C4244: “初始化”: 从“INT_PTR”转换到“int”，可能丢失数据
Bill.cpp(596): warning C4244: “+=”: 从“DOUBLE”转换到“LONG”，可能丢失数据
Bill.cpp(597): warning C4244: “+=”: 从“DOUBLE”转换到“LONG”，可能丢失数据
Bill.cpp(621): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(644): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(646): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(650): warning C4244: “参数”: 从“double”转换到“int”，可能丢失数据
Bill.cpp(650): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(653): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(667): warning C4244: “参数”: 从“double”转换到“int”，可能丢失数据
Bill.cpp(667): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(673): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(688): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(694): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(695): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(696): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(697): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(698): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(699): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(700): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(701): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(739): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(740): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(741): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(742): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(743): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(744): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(745): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(746): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(747): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(750): warning C4244: “初始化”: 从“INT_PTR”转换到“int”，可能丢失数据
Bill.cpp(794): warning C4244: “+=”: 从“DOUBLE”转换到“LONG”，可能丢失数据
Bill.cpp(795): warning C4244: “+=”: 从“DOUBLE”转换到“LONG”，可能丢失数据
Bill.cpp(819): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(842): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(844): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(848): warning C4244: “参数”: 从“double”转换到“int”，可能丢失数据
Bill.cpp(848): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Bill.cpp(851): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
  BingLingDlg.cpp
  CCustomer.cpp
CCustomer.cpp(47): warning C4244: “=”: 从“INT_PTR”转换到“int”，可能丢失数据
CCustomer.cpp(175): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(182): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(189): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(192): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(193): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(194): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(195): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(196): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(197): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(198): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(199): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(232): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(233): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(234): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(235): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(236): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(237): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(238): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(239): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(240): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(242): warning C4244: “初始化”: 从“INT_PTR”转换到“int”，可能丢失数据
CCustomer.cpp(291): warning C4244: “+=”: 从“DOUBLE”转换到“LONG”，可能丢失数据
CCustomer.cpp(292): warning C4244: “+=”: 从“DOUBLE”转换到“LONG”，可能丢失数据
CCustomer.cpp(316): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(340): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(342): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(346): warning C4244: “参数”: 从“double”转换到“int”，可能丢失数据
CCustomer.cpp(346): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(349): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(364): warning C4244: “参数”: 从“double”转换到“int”，可能丢失数据
CCustomer.cpp(364): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(370): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(384): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(388): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(389): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(390): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(391): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(392): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(393): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(394): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(395): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(429): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(430): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(431): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(432): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(433): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(434): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(435): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(436): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(437): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(439): warning C4244: “初始化”: 从“INT_PTR”转换到“int”，可能丢失数据
CCustomer.cpp(488): warning C4244: “+=”: 从“DOUBLE”转换到“LONG”，可能丢失数据
CCustomer.cpp(489): warning C4244: “+=”: 从“DOUBLE”转换到“LONG”，可能丢失数据
CCustomer.cpp(513): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(535): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(537): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(541): warning C4244: “参数”: 从“double”转换到“int”，可能丢失数据
CCustomer.cpp(541): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(544): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
CCustomer.cpp(676): warning C4244: “return”: 从“INT_PTR”转换到“int”，可能丢失数据
  CDetailed.cpp
  CMoneyEdit.cpp
  ComboBoxArea.cpp
ComboBoxArea.cpp(86): warning C4302: “类型转换”: 从“LPWSTR”到“WORD”截断
  ComboBoxIs.cpp
ComboBoxIs.cpp(120): warning C4302: “类型转换”: 从“LPWSTR”到“WORD”截断
  Comm.cpp
  ConfigFile.cpp
  COtherOfBill.cpp
  CSearchCustEdit.cpp
  CSearchDlg.cpp
  CSearchList.cpp
  CustList.cpp
CustList.cpp(26): warning C4244: “初始化”: 从“INT_PTR”转换到“int”，可能丢失数据
CustList.cpp(104): warning C4018: “<”: 有符号/无符号不匹配
CustList.cpp(119): warning C4018: “<”: 有符号/无符号不匹配
CustList.cpp(136): warning C4018: “<”: 有符号/无符号不匹配
CustList.cpp(186): warning C4018: “>”: 有符号/无符号不匹配
CustList.cpp(200): warning C4018: “<=”: 有符号/无符号不匹配
CustList.cpp(219): warning C4244: “初始化”: 从“INT_PTR”转换到“int”，可能丢失数据
CustList.cpp(242): warning C4244: “初始化”: 从“INT_PTR”转换到“int”，可能丢失数据
  CustManager.cpp
  正在编译...
  CustomerDlg.cpp
CustomerDlg.cpp(188): warning C4244: “初始化”: 从“DWORD_PTR”转换到“int”，可能丢失数据
CustomerDlg.cpp(201): warning C4244: “初始化”: 从“DWORD_PTR”转换到“int”，可能丢失数据
CustomerDlg.cpp(213): warning C4244: “=”: 从“DWORD_PTR”转换到“long”，可能丢失数据
CustomerDlg.cpp(216): warning C4244: “=”: 从“DWORD_PTR”转换到“long”，可能丢失数据
CustomerDlg.cpp(251): warning C4244: “初始化”: 从“DWORD_PTR”转换到“int”，可能丢失数据
CustomerDlg.cpp(320): warning C4244: “初始化”: 从“DWORD_PTR”转换到“int”，可能丢失数据
  CustomerOfReturn.cpp
CustomerOfReturn.cpp(132): warning C4244: “return”: 从“INT_PTR”转换到“int”，可能丢失数据
  CustSort.cpp
  CWeightEdit.cpp
  DlgBaoGouTiWen.cpp
  DlgCollections.cpp
DlgCollections.cpp(63): warning C4244: “=”: 从“DWORD_PTR”转换到“int”，可能丢失数据
  DlgCollList.cpp
  DlgDataMing.cpp
  DlgGoodOfBill.cpp
  DlgPayedWage.cpp
  DlgPhone.cpp
  DlgReckon.cpp
  DlgReturn.cpp
  Dlg_ArrearsList.cpp
Dlg_ArrearsList.cpp(95): warning C4244: “初始化”: 从“DWORD_PTR”转换到“int”，可能丢失数据
  ExcelDoc.cpp
  ExpAddCustDlg.cpp
  ExpressBillDLG.cpp
  Goods.cpp
  GoodsOfBill.cpp
  GoodsOfOrder.cpp
  正在编译...
  Grain.cpp
  ListButton.cpp
  Log.cpp
Log.cpp(13): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
Log.cpp(51): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
Log.cpp(95): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
Log.cpp(96): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
Log.cpp(110): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
Log.cpp(111): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
Log.cpp(135): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
Log.cpp(154): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
  Order.cpp
Order.cpp(24): warning C4244: “return”: 从“INT_PTR”转换到“int”，可能丢失数据
  OrderDlg.cpp
  OtherGoods.cpp
  OtherGoodsOfBill.cpp
  Payroll.cpp
Payroll.cpp(163): warning C4244: “return”: 从“INT_PTR”转换到“int”，可能丢失数据
  PreParent.cpp
  PreView.cpp
PreView.cpp(162): warning C4244: “参数”: 从“double”转换到“int”，可能丢失数据
PreView.cpp(162): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
PreView.cpp(170): warning C4244: “参数”: 从“double”转换到“int”，可能丢失数据
PreView.cpp(170): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
PreView.cpp(190): warning C4244: “=”: 从“double”转换到“int”，可能丢失数据
  PriceEdit.cpp
  RBill.cpp
  Receipt.cpp
Receipt.cpp(172): warning C4244: “参数”: 从“double”转换到“int”，可能丢失数据
Receipt.cpp(172): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(177): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(183): warning C4244: “参数”: 从“double”转换到“int”，可能丢失数据
Receipt.cpp(183): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(188): warning C4244: “参数”: 从“double”转换到“int”，可能丢失数据
Receipt.cpp(188): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(192): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(195): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(199): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(202): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(208): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(211): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(215): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(221): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(224): warning C4244: “参数”: 从“double”转换到“int”，可能丢失数据
Receipt.cpp(244): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(247): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(252): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(268): warning C4244: “参数”: 从“double”转换到“int”，可能丢失数据
Receipt.cpp(268): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(273): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(283): warning C4244: “参数”: 从“double”转换到“int”，可能丢失数据
Receipt.cpp(283): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(287): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(290): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(294): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(297): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(303): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(306): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(310): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(316): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(319): warning C4244: “参数”: 从“double”转换到“int”，可能丢失数据
Receipt.cpp(339): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(342): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
Receipt.cpp(347): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
  SellGoodsDlg.cpp
  ShouKuanLei.cpp
ShouKuanLei.cpp(135): warning C4244: “=”: 从“INT_PTR”转换到“int”，可能丢失数据
ShouKuanLei.cpp(260): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(268): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(275): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(278): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(279): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(280): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(281): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(282): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(306): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(307): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(308): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(309): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(310): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(313): warning C4244: “初始化”: 从“INT_PTR”转换到“int”，可能丢失数据
ShouKuanLei.cpp(343): warning C4244: “+=”: 从“DOUBLE”转换到“LONG”，可能丢失数据
ShouKuanLei.cpp(344): warning C4244: “+=”: 从“DOUBLE”转换到“LONG”，可能丢失数据
ShouKuanLei.cpp(363): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(364): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(379): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(381): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(385): warning C4244: “参数”: 从“double”转换到“int”，可能丢失数据
ShouKuanLei.cpp(385): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(388): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(401): warning C4244: “参数”: 从“double”转换到“int”，可能丢失数据
ShouKuanLei.cpp(401): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(407): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(421): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(424): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(425): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(426): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(427): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(428): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(451): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(452): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(453): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(454): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(455): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(457): warning C4244: “初始化”: 从“INT_PTR”转换到“int”，可能丢失数据
ShouKuanLei.cpp(486): warning C4244: “+=”: 从“DOUBLE”转换到“LONG”，可能丢失数据
ShouKuanLei.cpp(487): warning C4244: “+=”: 从“DOUBLE”转换到“LONG”，可能丢失数据
ShouKuanLei.cpp(506): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(507): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(522): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(524): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(528): warning C4244: “参数”: 从“double”转换到“int”，可能丢失数据
ShouKuanLei.cpp(528): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(531): warning C4244: “参数”: 从“DOUBLE”转换到“int”，可能丢失数据
ShouKuanLei.cpp(546): warning C4244: “return”: 从“INT_PTR”转换到“int”，可能丢失数据
  SortEdit.cpp
  StealERP.cpp
  StealERPDlg.cpp
StealERPDlg.cpp(77): warning C4244: “初始化”: 从“DWORD_PTR”转换到“DWORD”，可能丢失数据
  SteelList.cpp
SteelList.cpp(8): warning C4244: “return”: 从“INT_PTR”转换到“int”，可能丢失数据
  SteelOfBill.cpp
  正在编译...
  SteelOfList.cpp
  Steels.cpp
  SteelsOfBill.cpp
  StellOfOrder.cpp
  Tile.cpp
  TileOfBill.cpp
  Wage.cpp
Wage.cpp(186): warning C4244: “return”: 从“INT_PTR”转换到“int”，可能丢失数据
  WeightInTonEdit.cpp
  正在生成代码
  All 1662 functions were compiled because no usable IPDB/IOBJ from previous compilation was found.
  已完成代码的生成
  StealERP.vcxproj -> E:\StealCRM\StealERP\x64\Release\StealERP.exe
