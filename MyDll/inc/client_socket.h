#pragma once
#include <string>
#include "noncopyable.h"

namespace wxhelper {
	class SocketInit :public NonCopyable {
	public:
		SocketInit();
		~SocketInit();
		bool valid_{ false };
	};

	static SocketInit kSocketInit;

	class TcpClient :public NonCopyable {
	public:
		TcpClient() = default;
		TcpClient(std::string ip, int pcrt);
		void SendAndCloseSocket(std::string& content);

	private:
		std::string ip_;
		int port_;
	};
}
