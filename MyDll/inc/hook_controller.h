#pragma once
#include "http_controller.h"

namespace wxhelper {

	class HookController :public http::HttpController<HookController> {
	public:
		PATHS_BEGIN
		ADD_PATH("/api/hookSyncMsg", HookSyncMsg);
		ADD_PATH("/api/unhookSyncMsg", UnHookSyncMsg);
		ADD_PATH("/api/hookLog", HookLog);
		ADD_PATH("/api/unhookLog", UnHookLog);
		PATHS_END
	public:
		/// @brief hook��Ϣ
		/// @param params json
		/// @return json
		static std::string HookSyncMsg(std::string params);
		/// @brief ȡ��hook��Ϣ
		/// @param params json
		/// @return json
		static std::string UnHookSyncMsg(std::string params);
		/// @brief hook��־��Ϣ
		/// @param params json
		/// @return json
		static std::string HookLog(std::string params);
		/// @brief ȡ��hook��־��Ϣ
		/// @param params json
		/// @return json
		static std::string UnHookLog(std::string params);

	};
}//namespace wxhelprt