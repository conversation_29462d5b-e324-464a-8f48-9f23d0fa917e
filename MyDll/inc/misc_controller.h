#pragma once
#include "http_controller.h"

namespace wxhelper {

	class MiscController :public http::HttpController<MiscController> {
	public:
		PATHS_BEGIN
			ADD_PATH("/api/checkLogin", CheckLogin);
			ADD_PATH("/api/userInfo", GetUserInfo);
			ADD_PATH("/api/getSNSFirstPage", GetSNSFirstPage);
			ADD_PATH("/api/getSNSNextPage", GetSNSNextPage);
			ADD_PATH("/api/addFavFromMsg", AddFavFromMsg);

			ADD_PATH("/api/addFavFromImage", AddFavFromImage);
			ADD_PATH("/api/decodeImage", DecodeImage);
			ADD_PATH("/api/getVoiceByMsgId", GetVoiceByMsgId);
			ADD_PATH("/api/ocr", DoOcrTask);

			ADD_PATH("/api/lockWeChat", LockWeChat);
			ADD_PATH("/api/unlockWeChat", UnlockWeChat);
			ADD_PATH("/api/clickEnterWeChat", ClickEnterWeChat);
			ADD_PATH("/api/getLoginUrl", GetLoginUrl);

			ADD_PATH("/api/translateVoice", TranslateVoice);
			ADD_PATH("/api/getTranslateVoiceText", GetTranslateVoiceText);
			ADD_PATH("/api/openUrlByWeChat", OpenUrlByWeChat);
			ADD_PATH("/api/confirmReceipt", ConfirmReceipt);
			ADD_PATH("/api/refuseReceipt", RefundReceipt);
			ADD_PATH("/api/downloadAttach", DownloadAttach);
			ADD_PATH("/api/verifyApply", VerifyApply);
		PATHS_END

	public:
		/// @brief ����Ƿ��¼
		/// @param params json
		/// @return json
		static std::string CheckLogin(std::string params);
		/// @brief ��ȡ��¼�û���Ϣ
		/// @param params json
		/// @return json
		static std::string GetUserInfo(std::string params);
		/// @brief ����Ȧ��ҳ
		/// @param params json
		/// @return json
		static std::string GetSNSFirstPage(std::string params);
		/// @brief ����Ȧ��һҳ
		/// @param params json
		/// @return json
		static std::string GetSNSNextPage(std::string params);
		/// @brief �ղ���Ϣ
		/// @param params json
		/// @return json
		static std::string AddFavFromMsg(std::string params);
		/// @brief �ղ�ͼƬ
		/// @param params json
		/// @return json
		static std::string AddFavFromImage(std::string params);
		/// @brief ����ͼƬ
		/// @param params json
		/// @return json
		static std::string DecodeImage(std::string params);
		/// @brief ��ȡ�����ļ�
		/// @param params json
		/// @return json
		static std::string GetVoiceByMsgId(std::string params);
		/// @brief ͼƬocr
		/// @param params json
		/// @return json
		static std::string DoOcrTask(std::string params);
		/// @brief ����΢��
		/// @param params json
		/// @return json
		static std::string LockWeChat(std::string params);
		/// @brief ����΢��
		/// @param params json
		/// @return json
		static std::string UnlockWeChat(std::string params);
		/// @brief ����΢��
		/// @param params json
		/// @return json
		static std::string ClickEnterWeChat(std::string params);
		/// @brief ��ȡ��¼url
		/// @param params json
		/// @return json
		static std::string GetLoginUrl(std::string params);
		/// @brief ����ת�ı�
		/// @param params json
		/// @return json
		static std::string TranslateVoice(std::string params);
		/// @brief ��ȡ����ת�ı����
		/// @param params json
		/// @return json
		static std::string GetTranslateVoiceText(std::string params);
		/// @brief ͨ���������url
		/// @param params json
		/// @return json
		static std::string OpenUrlByWeChat(std::string params);
		/// @brief ȷ���տ�
		/// @param params json
		/// @return json
		static std::string ConfirmReceipt(std::string params);
		/// @brief �ܾ��տ�
		/// @param params json
		/// @return json
		static std::string RefundReceipt(std::string params);
		/// @brief ���ظ���
		/// @param params json
		/// @return json
		static std::string DownloadAttach(std::string params);
		/// @brief ͨ������
		/// @param params json
		/// @return json
		static std::string VerifyApply(std::string params);
	};
}//namespace wxhelper