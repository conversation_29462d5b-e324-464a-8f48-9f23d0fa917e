{"Version": 1, "WorkspaceRootPath": "F:\\Code\\test\\injector-2\\MyDll\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{FB4371CA-2472-4503-8EF4-8F3B5B0FE651}|MyDll.vcxproj|F:\\Code\\test\\injector-2\\MyDll\\src\\wechat_db.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{FB4371CA-2472-4503-8EF4-8F3B5B0FE651}|MyDll.vcxproj|solutionrelative:src\\wechat_db.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{FB4371CA-2472-4503-8EF4-8F3B5B0FE651}|MyDll.vcxproj|F:\\Code\\test\\injector-2\\MyDll\\src\\misc_controller.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{FB4371CA-2472-4503-8EF4-8F3B5B0FE651}|MyDll.vcxproj|solutionrelative:src\\misc_controller.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{FB4371CA-2472-4503-8EF4-8F3B5B0FE651}|MyDll.vcxproj|F:\\Code\\test\\injector-2\\MyDll\\inc\\misc_controller.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{FB4371CA-2472-4503-8EF4-8F3B5B0FE651}|MyDll.vcxproj|solutionrelative:inc\\misc_controller.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{FB4371CA-2472-4503-8EF4-8F3B5B0FE651}|MyDll.vcxproj|F:\\Code\\test\\injector-2\\MyDll\\inc\\sqlite_function.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{FB4371CA-2472-4503-8EF4-8F3B5B0FE651}|MyDll.vcxproj|solutionrelative:inc\\sqlite_function.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{FB4371CA-2472-4503-8EF4-8F3B5B0FE651}|MyDll.vcxproj|F:\\Code\\test\\injector-2\\MyDll\\src\\thread_pool.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{FB4371CA-2472-4503-8EF4-8F3B5B0FE651}|MyDll.vcxproj|solutionrelative:src\\thread_pool.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{FB4371CA-2472-4503-8EF4-8F3B5B0FE651}|MyDll.vcxproj|F:\\Code\\test\\injector-2\\MyDll\\src\\sync_msg_hook.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{FB4371CA-2472-4503-8EF4-8F3B5B0FE651}|MyDll.vcxproj|solutionrelative:src\\sync_msg_hook.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Code\\c++\\wxhelper-master\\inc\\include\\utils.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Code\\c++\\wxhelper-master\\inc\\include\\thread_pool.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Code\\c++\\wxhelper-master\\inc\\include\\singleton.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{FB4371CA-2472-4503-8EF4-8F3B5B0FE651}|MyDll.vcxproj|F:\\Code\\test\\injector-2\\MyDll\\src\\wxutils.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{FB4371CA-2472-4503-8EF4-8F3B5B0FE651}|MyDll.vcxproj|solutionrelative:src\\wxutils.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{FB4371CA-2472-4503-8EF4-8F3B5B0FE651}|MyDll.vcxproj|F:\\Code\\test\\injector-2\\MyDll\\src\\dllMain.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{FB4371CA-2472-4503-8EF4-8F3B5B0FE651}|MyDll.vcxproj|solutionrelative:src\\dllMain.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{FB4371CA-2472-4503-8EF4-8F3B5B0FE651}|MyDll.vcxproj|F:\\Code\\test\\injector-2\\MyDll\\inc\\offset.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{FB4371CA-2472-4503-8EF4-8F3B5B0FE651}|MyDll.vcxproj|solutionrelative:inc\\offset.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{FB4371CA-2472-4503-8EF4-8F3B5B0FE651}|MyDll.vcxproj|F:\\Code\\test\\injector-2\\MyDll\\src\\wxhelper.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{FB4371CA-2472-4503-8EF4-8F3B5B0FE651}|MyDll.vcxproj|solutionrelative:src\\wxhelper.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{FB4371CA-2472-4503-8EF4-8F3B5B0FE651}|MyDll.vcxproj|F:\\Code\\test\\injector-2\\MyDll\\inc\\wechat_service.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{FB4371CA-2472-4503-8EF4-8F3B5B0FE651}|MyDll.vcxproj|solutionrelative:inc\\wechat_service.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Code\\c++\\wxhelper-master\\inc\\include\\noncopyable.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\Code\\c++\\wxhelper-master\\inc\\include\\memory.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{75188d03-9892-4ae2-abf1-207126247ce5}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "wechat_db.cpp", "DocumentMoniker": "F:\\Code\\test\\injector-2\\MyDll\\src\\wechat_db.cpp", "RelativeDocumentMoniker": "src\\wechat_db.cpp", "ToolTip": "F:\\Code\\test\\injector-2\\MyDll\\src\\wechat_db.cpp", "RelativeToolTip": "src\\wechat_db.cpp", "ViewState": "AgIAAKUAAAAAAAAAAAAAAKgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-05-02T10:07:41.259Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "sqlite_function.h", "DocumentMoniker": "F:\\Code\\test\\injector-2\\MyDll\\inc\\sqlite_function.h", "RelativeDocumentMoniker": "inc\\sqlite_function.h", "ToolTip": "F:\\Code\\test\\injector-2\\MyDll\\inc\\sqlite_function.h", "RelativeToolTip": "inc\\sqlite_function.h", "ViewState": "AgIAAF0AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-02T10:07:01.744Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "thread_pool.cpp", "DocumentMoniker": "F:\\Code\\test\\injector-2\\MyDll\\src\\thread_pool.cpp", "RelativeDocumentMoniker": "src\\thread_pool.cpp", "ToolTip": "F:\\Code\\test\\injector-2\\MyDll\\src\\thread_pool.cpp", "RelativeToolTip": "src\\thread_pool.cpp", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-05-02T10:05:06.608Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "sync_msg_hook.cpp", "DocumentMoniker": "F:\\Code\\test\\injector-2\\MyDll\\src\\sync_msg_hook.cpp", "RelativeDocumentMoniker": "src\\sync_msg_hook.cpp", "ToolTip": "F:\\Code\\test\\injector-2\\MyDll\\src\\sync_msg_hook.cpp", "RelativeToolTip": "src\\sync_msg_hook.cpp", "ViewState": "AgIAAA8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-05-02T10:05:01.627Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "wxhelper.cpp", "DocumentMoniker": "F:\\Code\\test\\injector-2\\MyDll\\src\\wxhelper.cpp", "RelativeDocumentMoniker": "src\\wxhelper.cpp", "ToolTip": "F:\\Code\\test\\injector-2\\MyDll\\src\\wxhelper.cpp*", "RelativeToolTip": "src\\wxhelper.cpp*", "ViewState": "AgIAAAYAAAAAAAAAAAAAABQAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-05-02T08:35:34.135Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "offset.h", "DocumentMoniker": "F:\\Code\\test\\injector-2\\MyDll\\inc\\offset.h", "RelativeDocumentMoniker": "inc\\offset.h", "ToolTip": "F:\\Code\\test\\injector-2\\MyDll\\inc\\offset.h", "RelativeToolTip": "inc\\offset.h", "ViewState": "AgIAAMwBAAAAAAAAAAAawN8BAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-04-17T07:03:42.896Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "misc_controller.h", "DocumentMoniker": "F:\\Code\\test\\injector-2\\MyDll\\inc\\misc_controller.h", "RelativeDocumentMoniker": "inc\\misc_controller.h", "ToolTip": "F:\\Code\\test\\injector-2\\MyDll\\inc\\misc_controller.h", "RelativeToolTip": "inc\\misc_controller.h", "ViewState": "AgIAAAIAAAAAAAAAAAAAAAgAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-04-17T07:02:20.458Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "wxutils.cpp", "DocumentMoniker": "F:\\Code\\test\\injector-2\\MyDll\\src\\wxutils.cpp", "RelativeDocumentMoniker": "src\\wxutils.cpp", "ToolTip": "F:\\Code\\test\\injector-2\\MyDll\\src\\wxutils.cpp*", "RelativeToolTip": "src\\wxutils.cpp*", "ViewState": "AgIAAKAAAAAAAAAAAAAgwKcAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-04-17T06:52:41.925Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "dllMain.cpp", "DocumentMoniker": "F:\\Code\\test\\injector-2\\MyDll\\src\\dllMain.cpp", "RelativeDocumentMoniker": "src\\dllMain.cpp", "ToolTip": "F:\\Code\\test\\injector-2\\MyDll\\src\\dllMain.cpp", "RelativeToolTip": "src\\dllMain.cpp", "ViewState": "AgIAAAYAAAAAAAAAAAAAAB0AAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-04-17T06:52:21.448Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "wechat_service.h", "DocumentMoniker": "F:\\Code\\test\\injector-2\\MyDll\\inc\\wechat_service.h", "RelativeDocumentMoniker": "inc\\wechat_service.h", "ToolTip": "F:\\Code\\test\\injector-2\\MyDll\\inc\\wechat_service.h", "RelativeToolTip": "inc\\wechat_service.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-04-16T13:54:02.375Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "misc_controller.cpp", "DocumentMoniker": "F:\\Code\\test\\injector-2\\MyDll\\src\\misc_controller.cpp", "RelativeDocumentMoniker": "src\\misc_controller.cpp", "ToolTip": "F:\\Code\\test\\injector-2\\MyDll\\src\\misc_controller.cpp", "RelativeToolTip": "src\\misc_controller.cpp", "ViewState": "AgIAAAEAAAAAAAAAAAASwA4AAAA+AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-04-16T13:53:46.726Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "utils.h", "DocumentMoniker": "F:\\Code\\c++\\wxhelper-master\\inc\\include\\utils.h", "RelativeDocumentMoniker": "..\\..\\..\\c++\\wxhelper-master\\inc\\include\\utils.h", "ToolTip": "F:\\Code\\c++\\wxhelper-master\\inc\\include\\utils.h", "RelativeToolTip": "..\\..\\..\\c++\\wxhelper-master\\inc\\include\\utils.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-04-16T13:51:42.914Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "thread_pool.h", "DocumentMoniker": "F:\\Code\\c++\\wxhelper-master\\inc\\include\\thread_pool.h", "RelativeDocumentMoniker": "..\\..\\..\\c++\\wxhelper-master\\inc\\include\\thread_pool.h", "ToolTip": "F:\\Code\\c++\\wxhelper-master\\inc\\include\\thread_pool.h", "RelativeToolTip": "..\\..\\..\\c++\\wxhelper-master\\inc\\include\\thread_pool.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-04-16T13:51:37.896Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "singleton.h", "DocumentMoniker": "F:\\Code\\c++\\wxhelper-master\\inc\\include\\singleton.h", "RelativeDocumentMoniker": "..\\..\\..\\c++\\wxhelper-master\\inc\\include\\singleton.h", "ToolTip": "F:\\Code\\c++\\wxhelper-master\\inc\\include\\singleton.h", "RelativeToolTip": "..\\..\\..\\c++\\wxhelper-master\\inc\\include\\singleton.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-04-16T13:51:33.745Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "noncopyable.h", "DocumentMoniker": "F:\\Code\\c++\\wxhelper-master\\inc\\include\\noncopyable.h", "RelativeDocumentMoniker": "..\\..\\..\\c++\\wxhelper-master\\inc\\include\\noncopyable.h", "ToolTip": "F:\\Code\\c++\\wxhelper-master\\inc\\include\\noncopyable.h", "RelativeToolTip": "..\\..\\..\\c++\\wxhelper-master\\inc\\include\\noncopyable.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-04-16T13:51:29.137Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "memory.h", "DocumentMoniker": "F:\\Code\\c++\\wxhelper-master\\inc\\include\\memory.h", "RelativeDocumentMoniker": "..\\..\\..\\c++\\wxhelper-master\\inc\\include\\memory.h", "ToolTip": "F:\\Code\\c++\\wxhelper-master\\inc\\include\\memory.h", "RelativeToolTip": "..\\..\\..\\c++\\wxhelper-master\\inc\\include\\memory.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-04-16T13:51:18.9Z"}]}, {"DockedWidth": 200, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{3822e751-eb69-4b0e-b301-595a9e4c74d5}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{4a9b7e51-aa16-11d0-a8c5-00a0c921a4d2}"}, {"$type": "Bookmark", "Name": "ST:0:0:{34e76e81-ee4a-11d0-ae2e-00a0c90fffc3}"}, {"$type": "Bookmark", "Name": "ST:0:0:{68487888-204a-11d3-87eb-00c04f7971a5}"}]}]}]}