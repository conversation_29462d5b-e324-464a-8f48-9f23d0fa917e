  chat_controller.cpp
  warning [TODO-0]  : F:\Code\test\injector-2\MyDll\src\chat_controller.cpp(83)
  chat_room_controller.cpp
  client_socket.cpp
  config.cpp
F:\Code\test\injector-2\MyDll\src\config.cpp(10,9): warning C4005: “SPDLOG_ACTIVE_LEVEL”: 宏重定义
      F:\Code\test\injector-2\MyDll\inc\spdlog\common.h(240,13):
      参见“SPDLOG_ACTIVE_LEVEL”的前一个定义
  
  contacts_controller.cpp
  db_controller.cpp
  dllMain.cpp
  hook.cpp
F:\Code\test\injector-2\MyDll\inc\wxutils.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“src/hook.cpp”)
  
  hook_controller.cpp
  http_client.cpp
  http_router.cpp
  http_server.cpp
  json_utils.cpp
F:\Code\test\injector-2\MyDll\src\json_utils.cpp(27,38): warning C4101: “e”: 未引用的局部变量
  memory.cpp
  misc_controller.cpp
  sync_msg_hook.cpp
F:\Code\test\injector-2\MyDll\inc\wxutils.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“src/sync_msg_hook.cpp”)
  
  thread_pool.cpp
  utils.cpp
F:\Code\test\injector-2\MyDll\src\utils.cpp(24,40): warning C4244: “参数”: 从“INT64”转换到“UINT”，可能丢失数据
F:\Code\test\injector-2\MyDll\src\utils.cpp(36,39): warning C4244: “参数”: 从“INT64”转换到“UINT”，可能丢失数据
F:\Code\test\injector-2\MyDll\src\utils.cpp(40,25): warning C4244: “参数”: 从“INT64”转换到“UINT”，可能丢失数据
F:\Code\test\injector-2\MyDll\src\utils.cpp(102,17): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
F:\Code\test\injector-2\MyDll\src\utils.cpp(253,13): warning C4018: “<”: 有符号/无符号不匹配
  wechat_db.cpp
F:\Code\test\injector-2\MyDll\inc\wxutils.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“src/wechat_db.cpp”)
  
F:\Code\test\injector-2\MyDll\src\wechat_db.cpp(275,4): warning C4474: sprintf_s: 格式字符串中传递的参数太多
      F:\Code\test\injector-2\MyDll\src\wechat_db.cpp(275,4):
      占位符和其参数预计 0 可变参数，但提供的却是 2 参数
  
F:\Code\test\injector-2\MyDll\src\wechat_db.cpp(385,31): warning C4018: “<”: 有符号/无符号不匹配
  wechat_service.cpp
F:\Code\test\injector-2\MyDll\inc\wxutils.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“src/wechat_service.cpp”)
  
  warning [TODO-0] InviteMemberToChatRoom : F:\Code\test\injector-2\MyDll\src\wechat_service.cpp(521)
  warning [TODO-1] CreateChatRoom : F:\Code\test\injector-2\MyDll\src\wechat_service.cpp(542)
  warning [TODO-2] QuitChatRoom : F:\Code\test\injector-2\MyDll\src\wechat_service.cpp(562)
  warning [TODO-3] GetSNSFirstPage : F:\Code\test\injector-2\MyDll\src\wechat_service.cpp(596)
  warning [TODO-4] GetSNSNextPage : F:\Code\test\injector-2\MyDll\src\wechat_service.cpp(610)
  warning [TODO-5] GetContactOrChatRoomNickname : F:\Code\test\injector-2\MyDll\src\wechat_service.cpp(717)
  warning [TODO-6] DoDownloadTask : F:\Code\test\injector-2\MyDll\src\wechat_service.cpp(769)
  warning [TODO-7] ForwardPublicMsg : F:\Code\test\injector-2\MyDll\src\wechat_service.cpp(878)
  warning [TODO-8] ForwardPublicMsgByMsgId : F:\Code\test\injector-2\MyDll\src\wechat_service.cpp(890)
  warning [TODO-9] DecodeImage : F:\Code\test\injector-2\MyDll\src\wechat_service.cpp(896)
  warning [TODO-10] GetVoiceByDB : F:\Code\test\injector-2\MyDll\src\wechat_service.cpp(978)
  warning [TODO-11] SendCustonEmotion : F:\Code\test\injector-2\MyDll\src\wechat_service.cpp(984)
  warning [TODO-12] SendApplet : F:\Code\test\injector-2\MyDll\src\wechat_service.cpp(1006)
  warning [TODO-13] DoOCRTask : F:\Code\test\injector-2\MyDll\src\wechat_service.cpp(1109)
  warning [TODO-14] SendMultiAtText : F:\Code\test\injector-2\MyDll\src\wechat_service.cpp(1184)
  warning [TODO-15] TranslateVoice : F:\Code\test\injector-2\MyDll\src\wechat_service.cpp(1270)
  warning [TODO-16] GetTranslateVoiceText : F:\Code\test\injector-2\MyDll\src\wechat_service.cpp(1311)
  warning [TODO-17] OpenUrlByWeChatBrowser : F:\Code\test\injector-2\MyDll\src\wechat_service.cpp(1335)
  warning [TODO-18] GetChatRoomMemberNickname : F:\Code\test\injector-2\MyDll\src\wechat_service.cpp(1361)
  warning [TODO-19] DelContact : F:\Code\test\injector-2\MyDll\src\wechat_service.cpp(1368)
  warning [TODO-20] SearchContact : F:\Code\test\injector-2\MyDll\src\wechat_service.cpp(1378)
  warning [TODO-21] AddFriendByWxid : F:\Code\test\injector-2\MyDll\src\wechat_service.cpp(1395)
  warning [TODO-22] VerifyApply : F:\Code\test\injector-2\MyDll\src\wechat_service.cpp(1403)
  warning [TODO-23] DoConfirmReceipt : F:\Code\test\injector-2\MyDll\src\wechat_service.cpp(1411)
F:\Code\test\injector-2\MyDll\src\wechat_service.cpp(1431,22): warning C4244: “=”: 从“uint64_t”转换到“int”，可能丢失数据
  warning [TODO-24] DoRefuseReceipt : F:\Code\test\injector-2\MyDll\src\wechat_service.cpp(1439)
F:\Code\test\injector-2\MyDll\src\wechat_service.cpp(1461,21): warning C4244: “=”: 从“uint64_t”转换到“int”，可能丢失数据
  正在编译...
  wxhelper.cpp
F:\Code\test\injector-2\MyDll\src\wxhelper.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
F:\Code\test\injector-2\MyDll\inc\wxutils.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“src/wxhelper.cpp”)
  
  wxutils.cpp
F:\Code\test\injector-2\MyDll\inc\wxutils.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“src/wxutils.cpp”)
  
  正在生成代码
  Previous IPDB not found, fall back to full compilation.
  All 7935 functions were compiled because no usable IPDB/IOBJ from previous compilation was found.
  已完成代码的生成
detours.lib(detours.obj) : warning LNK4099: 未找到 PDB“”(使用“detours.lib(detours.obj)”或在“”中寻找)；正在链接对象，如同没有调试信息一样
detours.lib(disasm.obj) : warning LNK4099: 未找到 PDB“”(使用“detours.lib(disasm.obj)”或在“”中寻找)；正在链接对象，如同没有调试信息一样
detours.lib(modules.obj) : warning LNK4099: 未找到 PDB“”(使用“detours.lib(modules.obj)”或在“”中寻找)；正在链接对象，如同没有调试信息一样
  MyDll.vcxproj -> F:\Code\test\injector-2\MyDll\x64\Release\MyDll.dll
  'pwsh.exe' 不是内部或外部命令，也不是可运行的程序
  或批处理文件。
