2025-06-14 00:39:09 [info] [13788] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 00:39:10 [info] [13788] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 00:39:10 [info] [13788] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-14 00:39:13 [info] [7188] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 00:47:38 [info] [26404] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 00:47:39 [info] [26404] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 00:47:40 [info] [26404] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-14 00:55:17 [info] [12240] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 00:55:18 [info] [12240] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 00:55:18 [info] [12240] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-14 00:57:28 [info] [29544] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 00:57:29 [info] [29544] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 00:57:29 [info] [29544] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-14 00:57:32 [info] [29684] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 01:04:00 [info] [27576] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 01:04:01 [info] [27576] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 01:04:01 [info] [27576] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-14 01:04:08 [info] [35824] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 01:32:26 [info] [44500] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 01:32:27 [info] [44500] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 01:32:27 [info] [44500] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
