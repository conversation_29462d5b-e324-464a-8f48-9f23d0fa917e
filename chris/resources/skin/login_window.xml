<?xml version="1.0" encoding="utf-8"?>
<Window size="400,500" caption="0,0,0,0" sizebox="0,0,0,0" roundcorner="8,8" mininfo="400,500" maxinfo="400,500">
	<VerticalLayout bkcolor="#FF2E2E2E" borderround="8,8" padding="20,20,20,20">
	<!--标题区域-->
		<HorizontalLayout height="60" padding="0,0,0,20">
			<VerticalLayout>
				<Label text="微信登录" textcolor="#FFFFFFFF" font="2" align="center" valign="center"/>
			</VerticalLayout>
		</HorizontalLayout>
		
		<!--状态文本-->
		<HorizontalLayout height="30" padding="0,0,0,10">
			<Label name="statusLabel" text="正在检查登录状态..." textcolor="#FFCCCCCC" font="0" align="center" valign="center"/>
		</HorizontalLayout>
		
		<!--二维码显示区域	-->
		<VerticalLayout>
			<Container bkcolor="#FFFFFFFF" padding="20,20,20,20">
				<Label name="qrCodeImage" text="二维码加载中..." textcolor="#FF333333" font="0" valign="center"/>
			</Container>
		</VerticalLayout>
		
		<!--按钮区域-->
		<HorizontalLayout height="40" padding="0,20,0,0">

			<Button name="refreshButton" text="刷新二维码" textcolor="#FFFFFFFF" bkcolor="#FF4CAF50" hotbkcolor="#FF45A049" pushedbkcolor="#FF3E8E41"
					borderround="4,4" width="120" height="35"/>
			<control width="20"/>
			<Button name="closeButton" text="取消" textcolor="#FFFFFFFF" bkcolor="#FFF44336"
					hotbkcolor="#FFE53935" pushedbkcolor="#FFD32F2F" borderround="4,4" width="80" height="35"/>
		</HorizontalLayout>
		
		<!--提示文本-->
		<HorizontalLayout height="40">
			<Label text="请使用微信扫描上方二维码完成登录" textcolor="#FFAAAAAA" font="0" align="center" valign="center"/>
		</HorizontalLayout>

	</VerticalLayout>

</Window>