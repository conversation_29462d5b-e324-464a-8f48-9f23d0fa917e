#pragma once
#include "WeChatApiClient.h"
#include <functional>
#include <memory>

namespace chris {
	namespace wechat{
		
		//��¼״̬ö��
		enum class LoginStatus {
			UNKNOWN,     //δ֪״̬
			NOT_LOGGED_IN, //δ��¼
			LOGGED_IN,   //�ѵ�¼
			CHECKING   //���ڼ���¼״̬
		};


		//��¼״̬�ص�
		using LoginStatusCallback = std::function<void(LoginStatus status, const std::wstring& error)>;
		//��ά���ȡ�ص�
		using QRCodeCallback = std::function<void(bool success, const std::wstring& qr_url, const std::wstring& error)>;
		//�û���Ϣ�ص�
		using UserInfoCallback = std::function<void(bool success, const std::wstring& user_info, const std::wstring& error)>;


		class WeChatManager {
		public:
			WeChatManager();
			~WeChatManager();

			//��ʼ��
			void Initialize(const std::wstring& api_base_url = L"http://127.0.0.1:19088");

			//����
			void Start();
			void Stop();

			//����¼״̬��һ���Լ�飩
			void CheckLoginStatus(LoginStatusCallback callback);

			//��ʼ��¼��أ����ڼ���¼״̬��
			void StartLoginMonitoring(LoginStatusCallback callback, DWORD check_interval_ms = 2000);

			//ֹͣ��¼���
			void StopLoginMonitoring();

			//��ȡ��¼��ά��
			void GetLoginQRCode(QRCodeCallback callback);

			//��ȡ�û���Ϣ
			void GetUserInfo(UserInfoCallback callback);

			//��ȡ��ǰ��¼״̬
			LoginStatus GetCurrentLoginStatus() const;

			//��ȡAPI�ͻ��ˣ����߼��û�ʹ�ã�
			chris::http::WeChatApiClient& GetApiClient();

		private:
			void OnLoginCheckResult(bool success, const std::wstring& response, const std::wstring& error);
			void LoginMonitoringLoop();

		private:
			std::unique_ptr<chris::http::WeChatApiClient> api_client_; //API�ͻ���
			LoginStatus current_status_; //��ǰ��¼״̬
			LoginStatusCallback login_callback_; //��¼״̬�ص�

			//��¼������
			std::atomic<bool> monitoring_active_; //�Ƿ����ڼ�ص�¼״̬
			std::thread monitoring_thread_; //����߳�
			DWORD check_interval_ms_; //�����ʱ�䣨���룩

			bool initialized_; //�Ƿ��ѳ�ʼ��
			mutable std::mutex status_mutex_; //״̬������
		};

	}// namespace wechat
}// namespace chris