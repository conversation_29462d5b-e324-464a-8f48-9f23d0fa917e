#include "WeChatApiClient.h"
#include <sstream>

namespace chris {
	namespace http {
		WeChatApiClient::WeChatApiClient()
			:initialized_(false), query_interval_ms_(500), send_interval_ms_(2000), manage_interval_ms_(1000) {
		}

		WeChatApiClient::~WeChatApiClient() {
			Stop();
		}

		void WeChatApiClient::Initialize(const std::wstring& base_url) {
			base_url_ = base_url;
			initialized_ = true;

			//���ô���ص�
			request_queue_.SetErrorCallback([](const std::wstring& error) {
				//TODO::�����־��¼
				OutputDebugString((L"WeChatApiClient Eror:" + error + L"\n").c_str());
				});
		}

		void WeChatApiClient::Start() {
			if (!initialized_) {
				return;
			}
			request_queue_.Start();
		}

		void WeChatApiClient::Stop() {
			request_queue_.Stop();
		}

		void WeChatApiClient::SetQueryInterval(DWORD interval_ms) {
			query_interval_ms_ = std::max(100UL, interval_ms);
		}

		void WeChatApiClient::SetSendinterval(DWORD interval_ms) {
			send_interval_ms_ = std::max(500UL, interval_ms);
		}

		void WeChatApiClient::SetManageInterval(DWORD interval_ms) {
			manage_interval_ms_ = std::max(300UL, interval_ms);
		}

		//����API����(��ѯ�ࣩ
		void WeChatApiClient::CheckLogin(WeChatApiCallback callback) {
			SendApiRequest(L"/api/checkLogin", L"{}", callback, ApiRequestType::QUERY);
		}

		void WeChatApiClient::GetUserInfo(WeChatApiCallback callback) {
			SendApiRequest(L"/api/userInfo", L"{}", callback, ApiRequestType::QUERY);
		}

		void WeChatApiClient::GetContactList(WeChatApiCallback callback) {
			SendApiRequest(L"/api/getContactList", L"{}", callback, ApiRequestType::QUERY);
		}

		//��Ϣ����API(������ - ���ϸ�ļ���ؼ���
		void WeChatApiClient::SendTextMessage(const std::wstring& wxid, const std::wstring& message, WeChatApiCallback callback) {
			std::map<std::wstring, std::wstring> params;
			params[L"wxid"] = wxid;
			params[L"msg"] = message;

			std::wstring json_data = CreateJsonRequest(params);

			SendApiRequest(L"/api/sendTextMsg", json_data, callback, ApiRequestType::SEND, 1);//������ȼ�
		}

		void WeChatApiClient::SendImageMessage(const std::wstring& wxid, const std::wstring& image_path, WeChatApiCallback callback) {
			std::map<std::wstring, std::wstring> params;
			params[L"wxid"] = wxid;
			params[L"imagePath"] = image_path;

			std::wstring json_data = CreateJsonRequest(params);
			SendApiRequest(L"/api/sendImagesMsg", json_data, callback, ApiRequestType::SEND, 1);//������ȼ�
		}

		void WeChatApiClient::SendFileMessage(const std::wstring& wxid, const std::wstring& file_path, WeChatApiCallback callback) {
			std::map<std::wstring, std::wstring> params;
			params[L"wxid"] = wxid;
			params[L"filepath"] = file_path;

			std::wstring json_data = CreateJsonRequest(params);
			SendApiRequest(L"/api/sendFileMsg", json_data, callback, ApiRequestType::SEND, 1);//������ȼ�
		}

		void WeChatApiClient::SendAtMessage(const std::wstring& room_id, const std::vector<std::wstring>& wxids, const std::wstring& message, WeChatApiCallback callback) {
			std::wstringstream ss;
			ss << L"{\"chatRoomId\":\"" << room_id << L"\",\"msg\":\"" << message << L"\",\"wxids\":[";
			for (size_t i = 0; i < wxids.size(); ++i) {
				if (i > 0) ss << L",";
				ss << L"\"" << wxids[i] << L"\"";
			}
			ss << L"]}";

			SendApiRequest(L"/api/SendAtText", ss.str(), callback, ApiRequestType::SEND, 1);//������ȼ�
		}

		void WeChatApiClient::SendPatMessage(const std::wstring& receiver, const std::wstring& wxid, WeChatApiCallback callback) {
			std::map<std::wstring, std::wstring> params;
			params[L"receiver"] = receiver;
			params[L"wxid"] = wxid;

			std::wstring json_data = CreateJsonRequest(params);
			SendApiRequest(L"/api/sendPatMsg", json_data, callback, ApiRequestType::SEND, 2);//�ϸ����ȼ�
		}

		void WeChatApiClient::ForwardMessage(const std::wstring& wxid, int64_t msg_id, WeChatApiCallback callback) {
			std::wstringstream ss;
			ss << L"{\"wxid\":\"" << wxid << L"\",\"msgId\":" << msg_id << L"}";

			SendApiRequest(L"/api/forwardMsg", ss.str(), callback, ApiRequestType::SEND, 2);//�ϸ����ȼ�
		}

		//Ⱥ��API(��ѯ��͹������ϣ�
		void WeChatApiClient::GetChatRoomInfo(const std::wstring& room_id, WeChatApiCallback callback) {
			std::map<std::wstring, std::wstring> params;
			params[L"chatRoomId"] = room_id;

			std::wstring json_data = CreateJsonRequest(params);
			SendApiRequest(L"/api/getChatRoomDetailInfo", json_data, callback, ApiRequestType::QUERY);
		}

		void WeChatApiClient::GetChatRoomMembers(const std::wstring& room_id, WeChatApiCallback callback) {
			std::map<std::wstring, std::wstring> params;
			params[L"chatRoomId"] = room_id;

			std::wstring json_data = CreateJsonRequest(params);
			SendApiRequest(L"/api/getMemberFromChatRoom", json_data, callback, ApiRequestType::QUERY);
		}

		void WeChatApiClient::AddMemberToChatRoom(const std::wstring& room_id, const std::vector<std::wstring>& member_ids, WeChatApiCallback callback) {
			std::wstringstream ss;
			ss << L"{\"chatRoomId\":\"" << room_id << L"\",\"memberIds\":[";
			for (size_t i = 0; i < member_ids.size(); ++i) {
				if (i > 0) ss << L",";
				ss << L"\"" << member_ids[i] << L"\"";
			}
			ss << L"]}";

			SendApiRequest(L"/api/addMemberToChatRoom", ss.str(), callback, ApiRequestType::MANAGE, 3);
		}

		void WeChatApiClient::RemoveMemberFromChatRoom(const std::wstring& room_id, const std::vector<std::wstring>& member_ids, WeChatApiCallback callback) {
			std::wstringstream ss;
			ss << L"{\"chatRoomId\":\"" << room_id << L"\",\"memberIds\":[";
			for (size_t i = 0; i < member_ids.size(); ++i) {
				if (i > 0) ss << L",";
				ss << L"\"" << member_ids[i] << L"\"";
			}
			ss << L"]}";

			SendApiRequest(L"/api/delMemberFromChatRoom", ss.str(), callback, ApiRequestType::MANAGE, 3);
		}

		void WeChatApiClient::CreateChatRoom(const std::vector<std::wstring>& member_ids, WeChatApiCallback callback) {
			std::wstringstream ss;
			ss << L"{\"memberIds\":[";
			for (size_t i = 0; i < member_ids.size(); ++i) {
				if (i > 0)ss << L",";
				ss << L"\"" << member_ids[i] << L"\"";
			}
			ss << L"]}";

			SendApiRequest(L"/api/createChatRoom", ss.str(), callback, ApiRequestType::MANAGE, 3);
		}

		void WeChatApiClient::QuitChatRoom(const std::wstring& room_id, WeChatApiCallback callback) {
			std::map<std::wstring, std::wstring> params;
			params[L"chatRoomId"] = room_id;

			std::wstring json_data = CreateJsonRequest(params);
			SendApiRequest(L"/api/quitChatRoom", json_data, callback, ApiRequestType::MANAGE, 3);
		}

		//��ϵ��API(��ѯ��͹������ϣ�
		void WeChatApiClient::GetContactProfile(const std::wstring& wxid, WeChatApiCallback callback) {
			std::map<std::wstring, std::wstring> params;
			params[L"wxid"] = wxid;

			std::wstring json_data = CreateJsonRequest(params);
			SendApiRequest(L"/api/getContactProfile", json_data, callback, ApiRequestType::QUERY);
		}

		void WeChatApiClient::SearchContact(const std::wstring& keyword, WeChatApiCallback callback) {
			std::map<std::wstring, std::wstring> params;
			params[L"keyword"] = keyword;

			std::wstring json_data = CreateJsonRequest(params);
			SendApiRequest(L"/api/searchContact", json_data, callback, ApiRequestType::QUERY);
		}


		void WeChatApiClient::AddContact(const std::wstring& wxid, const std::wstring& message, WeChatApiCallback callback) {
			std::map<std::wstring, std::wstring> params;
			params[L"wxid"] = wxid;
			params[L"msg"] = message;

			std::wstring json_data = CreateJsonRequest(params);
			SendApiRequest(L"/api/addContact", json_data, callback, ApiRequestType::MANAGE, 4);
		}

		void WeChatApiClient::DeleteContact(const std::wstring& wxid, WeChatApiCallback callback) {
			std::map<std::wstring, std::wstring> params;
			params[L""] = wxid;

			std::wstring json_data = CreateJsonRequest(params);
			SendApiRequest(L"/api/delContact", json_data, callback, ApiRequestType::MANAGE, 4);
		}

		void WeChatApiClient::VerifyApply(const std::wstring& v3, const std::wstring& v4, int permission, WeChatApiCallback callback) {
			std::wstringstream ss;
			ss << L"{\"v3\":\"" << v3 << L"\",\"v4\":\"" << v4 << L"\",\"permission\":" << permission << L"}";

			SendApiRequest(L"/api/verifyApply", ss.str(), callback, ApiRequestType::MANAGE, 4);
		}

		//���ݿ�API(��ѯ�ࣩ
		void WeChatApiClient::GetDatabaseInfo(WeChatApiCallback callback) {
			SendApiRequest(L"/api/GetDBInfo", L"{}", callback, ApiRequestType::QUERY);
		}

		void WeChatApiClient::ExecuteSQL(int64_t db_handle, const std::wstring& sql, WeChatApiCallback callback) {
			std::wstringstream ss;
			ss << L"{\"dbHandle\":" << db_handle << L",\"sql\":\"" << sql << L"\"}";

			SendApiRequest(L"/api/execSql", ss.str(), callback, ApiRequestType::QUERY);
		}

		//HOOK API
		void WeChatApiClient::EnableMessageHook(WeChatApiCallback callback) {
			SendApiRequest(L"/api/hookSyncMsg", L"{}", callback, ApiRequestType::MANAGE);
		}

		void WeChatApiClient::DisableMessageHook(WeChatApiCallback callback) {
			SendApiRequest(L"/api/unhookSyncMsg", L"{}", callback, ApiRequestType::MANAGE);
		}

		//��������API
		void WeChatApiClient::GetSNSFirstPage(WeChatApiCallback callback) {
			SendApiRequest(L"/api/getSNSFirstPage", L"{}", callback, ApiRequestType::QUERY);
		}

		void WeChatApiClient::GetSNSNextPage(int64_t sns_id, WeChatApiCallback callback) {
			std::wstringstream ss;
			ss << L"{\"snsId\":" << sns_id << L"}";

			SendApiRequest(L"/api/getSNSNextPage", ss.str(), callback, ApiRequestType::QUERY);
		}

		void WeChatApiClient::DoOCRTask(const std::wstring& image_path, WeChatApiCallback callback) {
			std::map<std::wstring, std::wstring> params;
			params[L"imagePath"] = image_path;

			std::wstring json_data = CreateJsonRequest(params);
			SendApiRequest(L"/api/ocr", json_data, callback, ApiRequestType::QUERY);
		}

		void WeChatApiClient::LockWeChat(WeChatApiCallback callback) {
			SendApiRequest(L"/api/lockWeChat", L"{}", callback, ApiRequestType::MANAGE);
		}

		void WeChatApiClient::UnlockWeChat(WeChatApiCallback callback) {
			SendApiRequest(L"/api/unlockWeChat", L"{}", callback, ApiRequestType::MANAGE);
		}

		void WeChatApiClient::GetLoginUrl(WeChatApiCallback callback) {
			SendApiRequest(L"/api/getLoginUrl", L"{}", callback, ApiRequestType::QUERY);
		}

		//��ȡ����״̬
		size_t WeChatApiClient::GetQueueSize() const {
			return request_queue_.GetQueueSize();
		}

		bool WeChatApiClient::IsRunning() const {
			return request_queue_.IsRunning();
		}

		void WeChatApiClient::ClearQueue() {
			request_queue_.ClearQueue();
		}

		//˽�з���ʵ��
		void WeChatApiClient::SendApiRequest(const std::wstring& endpoint, const std::wstring& json_data, WeChatApiCallback callback, ApiRequestType type, int priority) {
			if (!initialized_) {
				if (callback) {
					callback(false, L"", L"WeChatApiClient not initialized");
				}
				return;
			}

			//����HTTP����
			HttpRequest request;
			request.SetUrl(base_url_ + endpoint);
			request.SetMethod(HttpMethod::POST);
			request.SetJsonBody(json_data);
			request.SetTimeout(10000); //���ó�ʱʱ��Ϊ10��

			//�����������������Ż���
			if (priority == 5) {		//���û��ָ���Ż�����ʹ��Ĭ���Ż���
				priority = GetPriorityByType(type);
			}
			request.SetPriority(priority);


			//��������ID����׷��
			static int request_counter = 0;
			std::wstring request_id = L"req_" + std::to_wstring(++request_counter);
			request.SetRequestId(request_id);

			//���������������ò�ͬ�Ķ��м��
			DWORD interval = GetIntervalByType(type);
			request_queue_.SetRequestInterval(interval);

			//������Ӧ�ص���װ��
			auto response_callback = [this, callback](const HttpResponse& response) {
				HandleApiResponse(response, callback);
				};

			//������������
			request_queue_.EnqueueRequest(request, response_callback, priority);
		}

		std::wstring WeChatApiClient::CreateJsonRequest(const std::map<std::wstring, std::wstring>& params) {
			std::wstringstream ss;
			ss << L"{";

			bool first = true;
			for (const auto& param : params) {
				if (!first) {
					ss << L",";
				}
				ss << L"\"" << param.first << L"\":\"" << param.second << L"\"";
				first = false;
			}

			ss << L"}";
			return ss.str();
		}

		void WeChatApiClient::HandleApiResponse(const HttpResponse& response, WeChatApiCallback callback) {
			if (!callback) {
				return;
			}

			if (response.HasError()) {
				callback(false, L"", response.GetError());
				return;
			}

			if (!response.IsSuccess()) {
				std::wstring error = L"HTTP Error:" + std::to_wstring(response.GetStatusCode());
				callback(false, L"", error);
				return;
			}

			//����JSON��Ӧ
			std::wstring response_body = response.GetBody();

			//�򵥵�JSON���������code�ֶ�
			//�������ʹ�ø����Ƶ�JSON�⣬��Ϊ�˱��ּ򵥣�ʹ���ַ�������
			size_t code_pos = response_body.find(L"\"code\":");
			if (code_pos != std::wstring::npos) {
				size_t code_start = code_pos + 7;	//��code": �ĳ�����7
				size_t code_end = response_body.find_first_of(L",}", code_start);

				if (code_end != std::wstring::npos) {
					std::wstring code_str = response_body.substr(code_start, code_end - code_start);

					//�Ƴ����ܵĿո������
					code_str.erase(0, code_str.find_first_not_of(L" \t\""));
					code_str.erase(code_str.find_last_not_of(L" \t\"") + 1);

					try {
						int code = std::stoi(code_str);
						bool success = (code == 1 || code == 200);	//����MyDll�ķ������жϳɹ�

						if (success) {
							callback(true, response_body, L"");
						}
						else {
							//������ȡ������Ϣ
							std::wstring error_msg = L"API returned error code: " + std::to_wstring(code);

							size_t msg_pos = response_body.find(L"\"msg\":");
							if (msg_pos != std::wstring::npos) {
								size_t msg_start = response_body.find(L"\"", msg_pos + 6) + 1;
								size_t msg_end = response_body.find(L"\"", msg_start);
								if (msg_start != std::wstring::npos && msg_end != std::wstring::npos) {
									error_msg = response_body.substr(msg_start, msg_end - msg_start);
								}
							}

							callback(false, response_body, error_msg);
						}
					}
					catch (const std::exception&) {
						callback(false, response_body, L"Invalid response format");
					}
				}
				else {
					//û���ҵ�code�ֶΣ�����ɹ�
					callback(true, response_body, L"");
				}
			}

		}


		int WeChatApiClient::GetPriorityByType(ApiRequestType type) {

			switch (type) {
			case ApiRequestType::SEND:
				return 1; //������ȼ�
			case ApiRequestType::MANAGE:
				return 3; //�е����ȼ�
			case ApiRequestType::QUERY:
				return 5; //������ȼ�
			default:
				return 5; //Ĭ��������ȼ�
			}
		}

		DWORD WeChatApiClient::GetIntervalByType(ApiRequestType type) {
			switch (type) {
			case ApiRequestType::SEND:
				return send_interval_ms_; //��������ļ��
			case ApiRequestType::MANAGE:
				return manage_interval_ms_; //��������ļ��
			case ApiRequestType::QUERY:
				return query_interval_ms_;
			default:
				return manage_interval_ms_; //Ĭ��ʹ�ù�������ļ��
			}

		}
	}		// namespace http
}			// namespace chris