#include "HttpClient.h"
#include <thread>
#include <sstream>


namespace chris {
	namespace http {
		HttpClient::HttpClient() :
			default_timeout_ms_(5000), proxy_port_(8080), use_proxy_(false) {
		}

		HttpClient::~HttpClient() {}

		HttpResponse HttpClient::SendRequest(const HttpRequest& request) {
			return ExecuteRequest(request);
		}

		void HttpClient::SendRequestAsync(const HttpRequest& request, HttpCallback callback) {
			std::thread([this, request, callback]() {
				HttpResponse response = ExecuteRequest(request);
				if (callback) {
					callback(response);
				}

				}).detach();
		}

		void HttpClient::SetDefaultTimeout(DWORD timeout_ms) {
			default_timeout_ms_ = timeout_ms;
		}

		void HttpClient::SetDefaultHeader(const std::wstring& key, const std::wstring& value) {
			default_headers_[key] = value;
		}

		void HttpClient::SetProxy(const std::wstring& proxy_server, DWORD proxy_port) {
			proxy_server_ = proxy_server;
			proxy_port_ = proxy_port;
			use_proxy_ = !proxy_server.empty();
		}

		HttpResponse HttpClient::ExecuteRequest(const HttpRequest& request) {
			HttpResponse response;
			response.SetRequestId(request.GetRequestId());

			HINTERNET hInternet = nullptr;
			HINTERNET hConnect = nullptr;
			HINTERNET hRequest = nullptr;

			try {
				//��ʼ��WinINet
				std::wstring proxy_info = proxy_server_ + L":" + std::to_wstring(proxy_port_);
				if (use_proxy_) {
					std::wstring proxy_info = proxy_server_ + L":" + std::to_wstring(proxy_port_);
					hInternet = InternetOpen(L"ChrisWeChatClient/1.0",
						INTERNET_OPEN_TYPE_PROXY,
						proxy_info.c_str(), nullptr, 0);
				}
				else {
					hInternet = InternetOpen(L"ChrisWeChatClient/1.0", INTERNET_OPEN_TYPE_PRECONFIG, nullptr, nullptr, 0);
				}

				if (!hInternet) {
					response.SetError(L"Failed to initialize WinInet");
					return response;
				}

				//���ó�ʱ
				DWORD timeout = request.GetTimeout() > 0 ? request.GetTimeout() : default_timeout_ms_;
				InternetSetOption(hInternet, INTERNET_OPTION_CONNECT_TIMEOUT, &timeout, sizeof(timeout));
				InternetSetOption(hInternet, INTERNET_OPTION_SEND_TIMEOUT, &timeout, sizeof(timeout));
				InternetSetOption(hInternet, INTERNET_OPTION_RECEIVE_TIMEOUT, &timeout, sizeof(timeout));

				//����URL
				URL_COMPONENTS urlComp = { 0 };
				urlComp.dwStructSize = sizeof(urlComp);
				urlComp.dwSchemeLength = -1;
				urlComp.dwHostNameLength = -1;
				urlComp.dwUrlPathLength = -1;
				urlComp.dwExtraInfoLength = -1;

				if (!InternetCrackUrl(request.GetUrl().c_str(), 0, 0, &urlComp)) {
					response.SetError(L"Invalid URL format");
					return response;
				}

				std::wstring hostname(urlComp.lpszHostName, urlComp.dwHostNameLength);
				std::wstring urlpath(urlComp.lpszUrlPath, urlComp.dwUrlPathLength);
				if (urlComp.lpszExtraInfo) {
					urlpath += std::wstring(urlComp.lpszExtraInfo, urlComp.dwExtraInfoLength);
				}

				//���ӵ�������
				hConnect = InternetConnect(hInternet, hostname.c_str(), urlComp.nPort, nullptr, nullptr, INTERNET_SERVICE_HTTP, 0, 0);

				if (!hConnect) {
					response.SetError(L"Failed to connect to server");
					return response;
				}

				//��������
				DWORD flags = INTERNET_FLAG_RELOAD | INTERNET_FLAG_NO_CACHE_WRITE;
				if (urlComp.nScheme == INTERNET_SCHEME_HTTPS) {
					flags |= INTERNET_FLAG_SECURE;
				}

				hRequest = HttpOpenRequest(hConnect, MethodToString(request.GetMethod()).c_str(),
					urlpath.c_str(), nullptr, nullptr, nullptr, flags, 0);
				if(!hRequest){
					response.SetError(L"Failed to create HTTP request");
					return response;
				}

				//��������ͷ
				auto headers = request.GetHeaders();
				//�ϲ�Ĭ������ͷ
				for (const auto& header : default_headers_) {
					if (headers.find(header.first) == headers.end()) {
						headers[header.first] = header.second;
					}
				}

				std::wstring header_string;
				for (const auto& header : headers) {
					header_string += header.first + L":" + header.second + L"\r\n";
				}

				if (!header_string.empty()) {
					HttpAddRequestHeaders(hRequest, header_string.c_str(),
						header_string.length(), HTTP_ADDREQ_FLAG_ADD);
				}

				//��������
				std::string body_utf8 = WStringToString(request.GetBody());
				BOOL result = HttpSendRequest(hRequest, nullptr, 0,
					body_utf8.empty() ? nullptr : (LPVOID)body_utf8.c_str(),
					body_utf8.length());

				if (!result) {
					response.SetError(L"Failed to send HTTP request");
					return response;
				}

				//��ȡ״̬��
				DWORD status_code = 0;
				DWORD size = sizeof(status_code);
				HttpQueryInfo(hRequest, HTTP_QUERY_STATUS_CODE | HTTP_QUERY_FLAG_NUMBER,
					&status_code, &size, nullptr);
				response.SetStatusCode(status_code);

				//��ȡ��Ӧ��
				std::string response_body;
				char buffer[4096];
				DWORD bytes_read;
				while (InternetReadFile(hRequest, buffer, sizeof(buffer), &bytes_read) && bytes_read > 0) {
					response_body.append(buffer, bytes_read);
				}

				response.SetBody(StringToWString(response_body));
			}
			catch (const std::exception& e) {
				response.SetError(StringToWString("Exception:" + std::string(e.what())));
			}

			//������Դ
			if (hRequest) InternetCloseHandle(hRequest);
			if (hConnect) InternetCloseHandle(hConnect);
			if (hInternet)InternetCloseHandle(hInternet);

			return response;
		}

		std::wstring HttpClient::MethodToString(HttpMethod method) {
			switch (method)
			{
			case HttpMethod::GET:return L"GET";
			case HttpMethod::POST:return L"POST";
			case HttpMethod::PUT:return L"PUT";
			case HttpMethod::HTTP_DELETE:return L"DELETE";
			default:return L"POST";
			}
		}

		std::string HttpClient::WStringToString(const std::wstring& wstr) {
			if (wstr.empty()) return std::string();
			int size_needed = WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), nullptr, 0, nullptr, nullptr);
			std::string str(size_needed, 0);
			WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), &str[0], size_needed, nullptr, nullptr);

			return str;
		}

		std::wstring HttpClient::StringToWString(const std::string& str) {
			if (str.empty()) return std::wstring();
			int size_needed = MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), nullptr, 0);
			std::wstring wstr(size_needed, 0);
			MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), &wstr[0], size_needed);

			return wstr;
		}

	}	// namespace http
}		// namespace chris