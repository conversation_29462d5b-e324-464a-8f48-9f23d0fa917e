#pragma once
#include "HttpRequest.h"
#include "HttpResponse.h"
#include "HttpClient.h"
#include <queue>
#include <mutex>
#include <condition_variable>
#include <thread>
#include <atomic>
#include <functional>
#include <chrono>
#include <algorithm>


namespace chris {
	namespace http {
		//������ṹ
		struct QueueItem {
			HttpRequest request;
			HttpCallback callback;
			std::chrono::steady_clock::time_point scheduled_time;
			int priority;

			QueueItem(const HttpRequest& req, HttpCallback cb, int prio = 5)
				:request(req), callback(cb), priority(prio) {
				scheduled_time = std::chrono::steady_clock::now();
			}
		};

		//�Ż����Ƚ���
		struct QueueItemComparator {
			bool operator()(const QueueItem& a, const QueueItem& b)const {
				if (a.priority != b.priority) {
					return a.priority > b.priority;// ���ȼ��ߵ���ǰ��
			}
				return a.scheduled_time > b.scheduled_time;// ͬһ���ȼ��£��ȵ�����ǰ��
			}
		};

		class RequestQueue {
		public:
			RequestQueue();
			~RequestQueue();

			//�������д���
			void Start();

			//ֹͣ���д���
			void Stop();

			//������󵽶���
			void EnqueueRequest(const HttpRequest& request, HttpCallback callback, int priority = 5);

			//����ӳ�����
			void EnqueueDelayedRequest(const HttpRequest& request, HttpCallback callback,
				DWORD delay_ms, int priority = 5);

			//����������ʱ��(����)
			void SetRequestInterval(DWORD interval_ms);

			//������󲢷���
			void SetMaxConcurrency(int max_concurrent);

			//��ȡ����״̬
			size_t GetQueueSize() const;
			bool IsRunning() const;

			//��ն���
			void ClearQueue();

			//���ô���ص�
			void SetErrorCallback(std::function<void(const std::wstring&)> error_callback);

			//��̬����������(������ʱ�޸ģ�
			void SetRequestIntervalDynamic(DWORD interval_ms);

			//��ȡ��ǰ�������
			DWORD GetCurrentInterval() const;

		private:
			void ProcessQueue();
			void ExecuteRequest(const QueueItem& item);

		private:
			std::priority_queue<QueueItem, std::vector<QueueItem>, QueueItemComparator> request_queue_;
			mutable std::mutex queue_mutex_;
			std::condition_variable queue_condition_;

			std::atomic<bool> running_;
			std::thread worker_thread_;

			HttpClient http_client_;

			DWORD request_interval_ms_;
			int max_concurrency_;
			std::atomic<int> current_concurrency_;

			std::chrono::steady_clock::time_point last_request_time_;
			std::function<void(const std::wstring&)> error_callback_;

			mutable std::mutex interval_mutex_;		//����������õĻ�����
		};
	}	//namespace http
}		//namespace chris	