<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="MainFrame.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="main.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="SearchEditUI.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="FriendItemUI.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="FriendsListUI.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="InitializationManager.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="SplashScreen.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="HttpRequest.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="HttpResponse.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="HttpClient.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="RequestQueue.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="WeChatApiClient.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="WeChatManager.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="MainFrame.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="SearchEditUI.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="FriendItemUI.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="FriendsListUI.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="SplashScreen.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="InitializationManager.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="HttpRequest.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="HttpResponse.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="HttpClient.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="RequestQueue.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="WeChatApiClient.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="WeChatManager.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="LoginWindow.h">
      <Filter>头文件</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="chris.rc">
      <Filter>资源文件</Filter>
    </ResourceCompile>
  </ItemGroup>
</Project>