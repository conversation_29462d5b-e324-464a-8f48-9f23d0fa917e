#include "MainFrame.h"
#include <dwmapi.h>

#pragma comment(lib,"dwmapi.lib")

MainFrame::MainFrame() :m_bStartMove(false), m_bIsMaxImized(false), m_bIsTopMost(false) {
	m_StartPt.x = 0;
	m_StartPt.y = 0;

	::SetRectEmpty(&m_rcNormalPostion);
}

MainFrame::~MainFrame()
{
	//�����˳�ʱ�ر�WeChat
	if (InitializationManager::IsWeChatRunningStatic()) {
		InitializationManager::CloseWeChatProcess();
	}
}


void MainFrame::InitWindow()
{

	

	//��Ӵ�����Ӱ
	DWMNCRENDERINGPOLICY ncrp = DWMNCRP_ENABLED;
	::DwmSetWindowAttribute(m_hWnd, DWMWA_NCRENDERING_POLICY, &ncrp, sizeof(ncrp));

	MARGINS margins = { 1,1,1,1 };
	::DwmExtendFrameIntoClientArea(m_hWnd, &margins);

	// ���໯����
	::SetWindowSubclass(m_hWnd, SubclassProc, 0, (DWORD_PTR)this);

	//��ʼ���ö�״̬
	SwitchTopButtonState(m_bIsTopMost);

	//����������ص�
	CSearchEditUI* pSearchEdit = static_cast<CSearchEditUI*>(m_PaintManager.FindControl(_T("searchEdit")));
	if (pSearchEdit)
	{
		pSearchEdit->SetCallback(this);
	}

	CSearchEditUI* pSearchEdit2 = static_cast<CSearchEditUI*>(m_PaintManager.FindControl(_T("searchEdit2")));
	if (pSearchEdit2) {
		pSearchEdit2->SetCallback(this);
	}



	//��ʼ����������
	InitTestFriendData();

	//��ȡ�����б�ؼ�
	CFriendsListUI* pFriendsList = static_cast<CFriendsListUI*>(m_PaintManager.FindControl(_T("friendsList")));

	if (pFriendsList) {
		pFriendsList->SetCallback(this);

		//���ô�ֱ������
		pFriendsList->EnableScrollBar(true, false);

		//���ù��������ԣ�ǿ����ʾ
		CScrollBarUI* pVerticalScrollBar = pFriendsList->GetVerticalScrollBar();
		if (pVerticalScrollBar) {
			pVerticalScrollBar->SetVisible(true);
			pVerticalScrollBar->SetEnabled(true);
		}

		//��Ӳ�������
		for (int i = 0; i < min(20, (int)m_testFriends.size()); i++) {
			pFriendsList->AddFriend(m_testFriends[i]);
		}
		//ǿ���ػ�
		//pFriendsList->NeedUpdate();
		pFriendsList->ForceRefresh();
	}

}



void MainFrame::OnSearchEditTextChanged(const CDuiString& sText) {
	//TODO �����ﴦ�������ı��仯
	//���磺���º����б�
}


void MainFrame::Notify(TNotifyUI& msg) {
	if (msg.sType == _T("click"))
	{
		CDuiString name = msg.pSender->GetName();

		switch (name.GetData()[0])
		{
		case 'c': // closebtn
			if (name == _T("closebtn"))
			{
				//�رհ�ť��������ȹر�WeChat
				if (InitializationManager::IsWeChatRunningStatic()) {
					InitializationManager::CloseWeChatProcess();
				}
				PostQuitMessage(0);
				Close();
				return;
			}
			break;

		case 'm': // maxbtn, minbtn
			if (name == _T("maxbtn"))
			{
				if (!m_bIsMaxImized) {
					// ���浱ǰ����λ�úʹ�С
					::GetWindowRect(m_hWnd, &m_rcNormalPostion);

					// ��ȡ��Ļ��С
					RECT rcWork;
					::SystemParametersInfo(SPI_GETWORKAREA, 0, &rcWork, 0);

					// ���ô���Ϊȫ��
					::SetWindowPos(m_hWnd, NULL, rcWork.left, rcWork.top, rcWork.right - rcWork.left, rcWork.bottom - rcWork.top, SWP_NOZORDER | SWP_NOACTIVATE);

					m_bIsMaxImized = true;

					// �л���ťͼ��Ϊ��ԭͼ��
					SwitchMaxButtonIcon(true);
				}
				else {
					// �ָ�����λ�úʹ�С
					::SetWindowPos(m_hWnd, NULL, m_rcNormalPostion.left, m_rcNormalPostion.top, m_rcNormalPostion.right - m_rcNormalPostion.left, m_rcNormalPostion.bottom - m_rcNormalPostion.top, SWP_NOZORDER | SWP_NOACTIVATE);

					m_bIsMaxImized = false;

					// �л���ťͼ��Ϊ���ͼ��
					SwitchMaxButtonIcon(false);
				}
				return;
			}
			else if (name == _T("minbtn"))
			{
				::ShowWindow(m_hWnd, SW_MINIMIZE);
				return;
			}
			break;

		case 't': // topbtn
			if (name == _T("topbtn"))
			{
				if (!m_bIsTopMost) {
					// ���ô���Ϊ�ö�
					::SetWindowPos(m_hWnd, HWND_TOPMOST, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE);
					m_bIsTopMost = true;
				}
				else {
					// ȡ�������ö�
					::SetWindowPos(m_hWnd, HWND_NOTOPMOST, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE);
					m_bIsTopMost = false;
				}
				SwitchTopButtonState(m_bIsTopMost);

				return;
			}
			break;
		}
	}

	__super::Notify(msg);
}

LRESULT MainFrame::OnClose(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL& bHandled) {
	bHandled = TRUE;
	return 0;
}

LRESULT MainFrame::HandleMessage(UINT uMsg, WPARAM wParam, LPARAM lParam)
{
	//�ڴ��ڴ��������У��û��ദ��������Ϣ
	static bool bWindowCreated = false;
	if (!bWindowCreated) {
		if (uMsg == WM_CREATE) {
			LRESULT lRes = __super::HandleMessage(uMsg, wParam, lParam);
			bWindowCreated = true;
			return lRes;
		}
		return __super::HandleMessage(uMsg, wParam, lParam);
	}

	LRESULT lRes = 0;
	BOOL bHandled = TRUE;
	switch (uMsg) {
	case WM_LBUTTONDOWN:
	{
		POINT pt = { GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam) };

		// �������Ƿ��ڿؼ���
		CControlUI* pControl = NULL;
		try {
			if (m_PaintManager.GetRoot() != NULL) {
				pControl = m_PaintManager.FindControl(pt);

				if (pControl) {
					// ����ǿɽ����ؼ����������϶�
					if (_tcscmp(pControl->GetClass(), _T("Button")) == 0 ||
						_tcscmp(pControl->GetClass(), _T("Edit")) == 0 ||
						_tcscmp(pControl->GetClass(), _T("Combo")) == 0 ||
						_tcscmp(pControl->GetClass(), _T("List")) == 0 ||
						_tcscmp(pControl->GetClass(), _T("RichEdit")) == 0 ||
						_tcscmp(pControl->GetClass(), _T("SearchEditUI")) == 0) { // ��Ӷ�SearchEditUI�ļ��
						return __super::HandleMessage(uMsg, wParam, lParam);
					}
				}

				// ���������ؼ���û���ҵ��ؼ�������������϶�
				ReleaseCapture();
				SendMessage(WM_NCLBUTTONDOWN, HTCAPTION, MAKELPARAM(pt.x, pt.y));
				return 0;
			}
		}
		catch (...) {
			OutputDebugString(_T("FindControl�쳣\n"));
		}
		break;
	}
	case WM_NCHITTEST:
	{
		// ��ȡ���ڴ�С
		RECT rcWindow;
		GetWindowRect(m_hWnd, &rcWindow);

		// �����������
		POINT pt = { GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam) };
		pt.x -= rcWindow.left;
		pt.y -= rcWindow.top;

		// ���ڿ��
		int width = rcWindow.right - rcWindow.left;
		int height = rcWindow.bottom - rcWindow.top;

		// ��Ե����������
		const int BORDER_WIDTH = 4;

		// �ж����λ��
		if (pt.y < BORDER_WIDTH)
		{
			if (pt.x < BORDER_WIDTH)
				return HTTOPLEFT;
			else if (pt.x > width - BORDER_WIDTH)
				return HTTOPRIGHT;
			else
				return HTTOP;
		}
		else if (pt.y > height - BORDER_WIDTH)
		{
			if (pt.x < BORDER_WIDTH)
				return HTBOTTOMLEFT;
			else if (pt.x > width - BORDER_WIDTH)
				return HTBOTTOMRIGHT;
			else
				return HTBOTTOM;
		}
		else if (pt.x < BORDER_WIDTH)
		{
			return HTLEFT;
		}
		else if (pt.x > width - BORDER_WIDTH)
		{
			return HTRIGHT;
		}

		// �ж��Ƿ��ڱ���������
		if (pt.y < 30) {
			// ��ȫ�ػ�ȡ��ť�ؼ�
			CButtonUI* pCloseBtn = NULL;
			CButtonUI* pMaxBtn = NULL;
			CButtonUI* pMinBtn = NULL;
			CButtonUI* pTopBtn = NULL;
			try {
				// ֻ����m_pRoot����ʱ�ų��Բ��ҿؼ�
				if (m_PaintManager.GetRoot() != NULL) {
					pCloseBtn = static_cast<CButtonUI*>(m_PaintManager.FindControl(_T("closebtn")));
					pMaxBtn = static_cast<CButtonUI*>(m_PaintManager.FindControl(_T("maxbtn")));
					pMinBtn = static_cast<CButtonUI*>(m_PaintManager.FindControl(_T("minbtn")));
					pTopBtn = static_cast<CButtonUI*>(m_PaintManager.FindControl(_T("topbtn")));
				}
			}
			catch (...) {}

			// ����Ƿ����κΰ�ť��
			if (pCloseBtn) {
				RECT rcBtn = pCloseBtn->GetPos();
				if (pt.x >= rcBtn.left && pt.x <= rcBtn.right && pt.y >= rcBtn.top && pt.y <= rcBtn.bottom) {
					return HTCLIENT;
				}
			}

			if (pMaxBtn) {
				RECT rcBtn = pMaxBtn->GetPos();
				if (pt.x >= rcBtn.left && pt.x <= rcBtn.right && pt.y >= rcBtn.top && pt.y <= rcBtn.bottom) {
					return HTCLIENT;
				}
			}

			if (pMinBtn) {
				RECT rcBtn = pMinBtn->GetPos();
				if (pt.x >= rcBtn.left && pt.x <= rcBtn.right && pt.y >= rcBtn.top && pt.y <= rcBtn.bottom) {
					return HTCLIENT;
				}
			}

			if (pTopBtn) {
				RECT rcBtn = pTopBtn->GetPos();
				if (pt.x >= rcBtn.left && pt.x <= rcBtn.right && pt.y >= rcBtn.top && pt.y <= rcBtn.bottom) {
					return HTCLIENT;
				}
			}

			// �����κΰ�ť�ϣ����ر�����
			return HTCAPTION;
		}

		// �������Ƿ��ڿؼ���
		CControlUI* pControl = NULL;
		try {
			if (m_PaintManager.GetRoot() != NULL) {
				pControl = m_PaintManager.FindControl(pt);

				// ���û���ҵ��ؼ��������Ǿ�̬�ı��������϶�
				if (pControl == NULL ||
					_tcscmp(pControl->GetClass(), _T("Text")) == 0 ||
					_tcscmp(pControl->GetClass(), _T("Label")) == 0) {
					return HTCAPTION;   // �����Ϸ�
				}
			}
		}
		catch (...) {}

		// �����ؼ����������϶�
		return HTCLIENT;
	}
	case WM_GETMINMAXINFO:
	{
		MINMAXINFO* pInfo = (MINMAXINFO*)lParam;
		pInfo->ptMinTrackSize.x = 400;  // ��С���
		pInfo->ptMinTrackSize.y = 300;  // ��С�߶�

		break;
	}
	case WM_LBUTTONDBLCLK:
	{
		POINT pt = { GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam) };

		// �������Ƿ��ڿؼ���
		CControlUI* pControl = NULL;
		try {
			if (m_PaintManager.GetRoot() != NULL) {
				pControl = m_PaintManager.FindControl(pt);

				// �����겻�ڿؼ��ϣ���������ͨControl�ϣ����л����/��ԭ״̬
				if (pControl == NULL ||
					!(_tcscmp(pControl->GetClass(), _T("Button")) == 0 ||
						_tcscmp(pControl->GetClass(), _T("Edit")) == 0 ||
						_tcscmp(pControl->GetClass(), _T("Combo")) == 0 ||
						_tcscmp(pControl->GetClass(), _T("List")) == 0 ||
						_tcscmp(pControl->GetClass(), _T("RichEdit")) == 0 ||
						_tcscmp(pControl->GetClass(), _T("SearchEditUI")) == 0)) { // ��Ӷ�SearchEditUI�ļ��
					if (!m_bIsMaxImized) {
						// ���浱ǰ����λ�úʹ�С
						::GetWindowRect(m_hWnd, &m_rcNormalPostion);

						// ��ȡ��Ļ��С
						RECT rcWork;
						::SystemParametersInfo(SPI_GETWORKAREA, 0, &rcWork, 0);

						// ���ô���Ϊȫ��
						::SetWindowPos(m_hWnd, NULL, rcWork.left, rcWork.top, rcWork.right - rcWork.left, rcWork.bottom - rcWork.top,
							SWP_NOZORDER | SWP_NOACTIVATE);

						m_bIsMaxImized = true;
					}
					else {
						// �ָ�����λ�úʹ�С
						::SetWindowPos(m_hWnd, NULL, m_rcNormalPostion.left, m_rcNormalPostion.top,
							m_rcNormalPostion.right - m_rcNormalPostion.left,
							m_rcNormalPostion.bottom - m_rcNormalPostion.top,
							SWP_NOZORDER | SWP_NOACTIVATE);

						m_bIsMaxImized = false;
					}

					//�л���ťͼ��
					if (m_bIsMaxImized) {
						SwitchMaxButtonIcon(true);
					}
					else {
						SwitchMaxButtonIcon(false);
					}

					// ��ȫ�ظ��²���
					try {
						if (m_PaintManager.GetRoot() != NULL) {
							m_PaintManager.GetRoot()->NeedUpdate();
							m_PaintManager.NeedUpdate();
						}
					}
					catch (...) {}

					return 0;
				}
			}
		}
		catch (...) {}
		break;
	}
	case WM_SIZE:
	{

		//��ⴰ���Ƿ����
		if (wParam == SIZE_MAXIMIZED && !m_bIsMaxImized) {
			m_bIsMaxImized = true;
			SwitchMaxButtonIcon(true);
		}
		else if (wParam == SIZE_RESTORED && m_bIsMaxImized) {
			m_bIsMaxImized = false;
			SwitchMaxButtonIcon(false);
		}

		CFriendsListUI* pFriendsList = static_cast<CFriendsListUI*>(m_PaintManager.FindControl(_T("friendsList")));
		if (pFriendsList) {
			pFriendsList->ForceRefresh();
		}


		// ���²���
		try {
			if (m_PaintManager.GetRoot() != NULL) {
				m_PaintManager.GetRoot()->NeedUpdate();
				m_PaintManager.NeedUpdate();
			}
		}
		catch (...) {}
		break;
	}
	case WM_EXITSIZEMOVE:
	{
		try {
			if (m_PaintManager.GetRoot() != NULL) {
				// ǿ���ػ���������
				::InvalidateRect(m_hWnd, NULL, TRUE);

				// ��ȡ��ǰ���λ��
				POINT pt;
				::GetCursorPos(&pt);
				::ScreenToClient(m_hWnd, &pt);

				// ģ������ƶ��¼�
				::PostMessage(m_hWnd, WM_MOUSEMOVE, 0, MAKELPARAM(pt.x, pt.y));

				// ���²���
				m_PaintManager.GetRoot()->NeedUpdate();
				m_PaintManager.NeedUpdate();
			}
		}
		catch (...) {}
		break;
	}
	default:
		bHandled = FALSE;
		break;
	}

	if (!bHandled) {
		// ��DuiLib������Ϣ
		if (m_PaintManager.MessageHandler(uMsg, wParam, lParam, lRes)) {
			return lRes;
		}

		// �û��ദ��������Ϣ
		return CWindowWnd::HandleMessage(uMsg, wParam, lParam);
	}

	return lRes;
}

void MainFrame::OnFriendsSelected(const CDuiString& sId)
{
	//�������ѡ���¼�
	OutputDebugString(_T("Selected friend:"));
	OutputDebugString(sId);
	OutputDebugString(_T("\n"));

	//TODO:ѡ�к��ѵĴ����߼�
}
void MainFrame::OnLoadMoreFriends(int nStartIndex, int nCount)
{
	//���ظ������
	CFriendsListUI* pFriendsList = static_cast<CFriendsListUI*>(m_PaintManager.FindControl(_T("friendsList")));
	if (!pFriendsList) return;

	//ģ������ݿ��������
	for (int i = nStartIndex; i < nStartIndex + nCount && i < (int)m_testFriends.size(); i++) {
		pFriendsList->AddFriend(m_testFriends[i]);
	}
}


void MainFrame::SwitchMaxButtonIcon(bool bMaxImized)
{
	CButtonUI* pMaxBtn = static_cast<CButtonUI*>(m_PaintManager.FindControl(_T("maxbtn")));
	if (pMaxBtn) {
		if (bMaxImized) {
			//�л�Ϊ��ԭͼ��
			pMaxBtn->SetNormalImage(_T("res='114' restype='png'"));
			pMaxBtn->SetHotImage(_T("res='114' restype='png' hotbkcolor='0x5FE2E2E2'"));
			pMaxBtn->SetPushedImage(_T("res='114' restype='png' pushedbkcolor='0x5FD1D1D1'"));
		}
		else {
			//�л�Ϊ���ͼ��
			pMaxBtn->SetNormalImage(_T("res='105' restype='png'"));
			pMaxBtn->SetHotImage(_T("res='105' restype='png' hotbkcolor='0x5FE2E2E2'"));
			pMaxBtn->SetPushedImage(_T("res='105' restype='png' pushedbkcolor='0x5FD1D1D1'"));
		}
	}


}

void MainFrame::SwitchTopButtonState(bool bTopMost)
{
	CButtonUI* pTopBtn = static_cast<CButtonUI*>(m_PaintManager.FindControl(_T("topbtn")));
	if (pTopBtn) {
		if (bTopMost) {
			//�ö�״̬-��ť���ָ���
			pTopBtn->SetNormalImage(_T("res='112' restype='png' bkcolor='0x5FE2E2E2'"));
			pTopBtn->SetHotImage(_T("res='112' restype='png' bkcolor='0x5FE2E2E2'"));
			pTopBtn->SetPushedImage(_T("res='112' restype='png' bkcolor='0x5FE2E2E2'"));
		}
		else {
			//���ö�״̬-��ť�ָ�����
			pTopBtn->SetNormalImage(_T("res='111' restype='png'"));
			pTopBtn->SetHotImage(_T("res='111' restype='png' hotbkcolor='0x5FE2E2E2'"));
			pTopBtn->SetPushedImage(_T("res='111' restype='png' pushedbkcolor='0x5FD1D1D1'"));
		}
	}
}



LRESULT CALLBACK MainFrame::SubclassProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam, UINT_PTR uIdSubclass, DWORD_PTR dwRefData)
{

	MainFrame* pThis = (MainFrame*)dwRefData;

	switch (uMsg) {
	case WM_NCACTIVATE:
	case WM_NCPAINT:
		// ����0��ʾ�����Լ�����ǿͻ����Ļ���
		return 0;
	}

	return ::DefSubclassProc(hWnd, uMsg, wParam, lParam);
}

void MainFrame::InitTestFriendData()
{
	//������������
	for (int i = 0; i < 100; i++) {
		FriendItemInfo info;
		info.sId.Format(_T("friend_%d"), i);
		info.sNickName.Format(_T("����%d"), i);
		info.sMessage.Format(_T("���Ժ���%d ��������Ϣ"), i);

		//���ʱ��
		int nTimeType = rand() % 5;
		if (nTimeType == 0)info.sTime = _T("�ո�");
		else if (nTimeType == 1)info.sTime.Format(_T("%d:%02d"), rand() % 24, rand() % 60);
		else if (nTimeType == 2)info.sTime = _T("����");
		else if (nTimeType == 3)info.sTime.Format(_T("����%d"), rand() % 7 + 1);
		else info.sTime.Format(_T("%d/%d"), rand() % 12 + 1, rand() % 28 + 1);

		info.sAvatarPath.Format(_T("file='F:\\Code\\test\\injector-2\\chris\\resources\\pic\\headimage.jpg' corner='5,5,5,5'"));

		m_testFriends.push_back(info);
	}
}
