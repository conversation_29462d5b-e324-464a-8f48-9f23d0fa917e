#pragma once

#include "UIlib.h"

using namespace DuiLib;

namespace DuiLib {

	//�����ı��仯֪ͨ�ӿ�
	class ISearchEditUICallback {
	public:
		virtual void OnSearchEditTextChanged(const CDuiString& sText) = 0;
	};

	class CSearchEditUI :public CHorizontalLayoutUI {
	public:
		CSearchEditUI();
		virtual ~CSearchEditUI();

		virtual LPCTSTR GetClass() const;
		virtual LPVOID GetInterface(LPCTSTR pstrName);
		virtual void SetAttribute(LPCTSTR pstrName, LPCTSTR pstrValue);

		//��дDoEvent���������¼�
		virtual void DoEvent(TEventUI& event);
		virtual void Init();
		virtual void PaintBorder(HDC hDC);

		//���á���ȡ�ı�
		void SetText(LPCTSTR pstrText);
		CDuiString GetText() const;

		//���á���ȡ��ʾ�ı�
		void SetTipText(LPCTSTR pstrTipText);
		CDuiString GetTipText() const;

		//���á���ȡ��ʾ�ı���ɫ
		void SetTipColor(DWORD dwColor);
		DWORD GetTipColor() const;

		//���á���ȡ����ͼ��
		void SetSearchIcon(LPCTSTR pstrImage);
		CDuiString GetSearchIcon() const;

		//���û�ȡ���ͼ��
		void SetClearIcon(LPCTSTR pstrImage);
		CDuiString GetClearIcon() const;

		//���û�ȡ��������ɫ
		void SetNormalBkColor(DWORD dwColor);
		DWORD GetNormalBkColor() const;

		//���û�ȡ���㱳��ɫ
		void SetFocusBkColor(DWORD dwColor);
		DWORD GetFocusBkColor() const;

		//���û�ȡ�߿���ɫ
		void SetBorderColor(DWORD dwColor);
		DWORD GetBorderColor() const;

		//���û�ȡԲ�Ǵ�С
		void SetBorderRound(SIZE cxyRound);
		SIZE GetBorderRound() const;

		//�����ı��仯�ص�
		void SetCallback(ISearchEditUICallback* pCallback);

		//�����ؼ��ľ�̬����
		static CControlUI* CreateControl(LPCTSTR pstrClass);

	protected:
		void NotifyTextChanged();
		void UpdateControlState();



		bool OnEditNotify(void* param);
		bool OnClearBtnNotify(void* param);
	private:
		CLabelUI* m_pIcon;			//����ͼ��
		CEditUI* m_pEdit;			//�༭��
		CButtonUI* m_pClearBtn;		//�����ť

		CDuiString m_sTipText;		//��ʾ�ı�
		DWORD m_dwTipColor;			//��ʾ�ı���ɫ
		DWORD m_dwNormalBkColor;		//��������ɫ
		DWORD m_dwFocusBkColor;			//���㱳��ɫ
		DWORD m_dwBorderColor;			//�߿���ɫ
		SIZE m_cxyBorderRound;			//Բ�Ǵ�С

		bool m_bFocused;				//�Ƿ��ý���

		ISearchEditUICallback* m_pCallback;		//�ı��仯�ص�
	};
}		//namespace DuiLib