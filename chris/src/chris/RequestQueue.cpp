#include "RequestQueue.h"

namespace chris {
	namespace http {

		RequestQueue::RequestQueue()
			:running_(false), request_interval_ms_(1000), max_concurrency_(3), current_concurrency_(0) {
			last_request_time_ = std::chrono::steady_clock::now() - std::chrono::milliseconds(request_interval_ms_);
		}

		RequestQueue::~RequestQueue() {
			Stop();
		}

		void RequestQueue::Start() {
			if (running_.load()) {
				return;
			}

			running_.store(true);
			worker_thread_ = std::thread(&RequestQueue::ProcessQueue, this);
		}

		void RequestQueue::Stop() {
			if (!running_.load()) {
				return;
			}

			running_.store(false);
			queue_condition_.notify_all();

			if (worker_thread_.joinable()) {
				worker_thread_.join();
			}
		}

		// ����������ʱ��(����)
		void RequestQueue::EnqueueRequest(const HttpRequest& request, HttpCallback callback, int priority) {
			std::lock_guard<std::mutex> lock(queue_mutex_);
			request_queue_.emplace(request, callback, priority);
			queue_condition_.notify_one();
		}

		// ����ӳ����󵽶���
		void RequestQueue::EnqueueDelayedRequest(const HttpRequest& request, HttpCallback callback,
			DWORD delay_ms, int priority) {
			QueueItem item(request, callback, priority);
			item.scheduled_time = std::chrono::steady_clock::now() + std::chrono::milliseconds(delay_ms);

			std::lock_guard<std::mutex> lock(queue_mutex_);
			request_queue_.push(item);
			queue_condition_.notify_one();
		}

		void RequestQueue::SetRequestInterval(DWORD interval_ms) {
			request_interval_ms_ = (std::max)(100UL, interval_ms);		//��С���100ms
		}

		void RequestQueue::SetMaxConcurrency(int max_concurrent) {
			max_concurrency_ = (std::max)(1, max_concurrent);
		}

		size_t RequestQueue::GetQueueSize() const {
			std::lock_guard<std::mutex> lock(queue_mutex_);
			return request_queue_.size();
		}

		bool RequestQueue::IsRunning() const {
			return running_.load();
		}

		void RequestQueue::ClearQueue() {
			std::lock_guard<std::mutex> lock(queue_mutex_);
			while (!request_queue_.empty()) {
				request_queue_.pop();
			}
		}

		void RequestQueue::SetErrorCallback(std::function<void(const std::wstring&)> error_callback) {
			error_callback_ = error_callback;
		}

		void RequestQueue::ProcessQueue() {
			while (running_.load()) {
				std::unique_lock<std::mutex> lock(queue_mutex_);

				//�ȴ��������������ֹͣ�ź�
				queue_condition_.wait(lock, [this] {
					return !request_queue_.empty() || !running_.load();
					});

				if (!running_.load()) {
					break;	//�˳������߳�
				}

				if (request_queue_.empty()) {
					continue;	//����Ϊ�գ������ȴ�
				}

				//��鲢��������
				if (current_concurrency_.load() >= max_concurrency_) {
					lock.unlock();
					std::this_thread::sleep_for(std::chrono::milliseconds(100));
					continue;
				}

				//��ȡ��һ������
				QueueItem item = request_queue_.top();
				request_queue_.pop();
				lock.unlock();

				//����Ƿ���ִ��ʱ��
				auto now = std::chrono::steady_clock::now();
				if (item.scheduled_time > now) {
					//���·Żض���
					lock.lock();
					request_queue_.push(item);
					lock.unlock();
					std::this_thread::sleep_for(std::chrono::milliseconds(100));
					continue;
				}

				//���������(ʹ�ö�̬�����
				DWORD current_interval;
				{
					std::lock_guard<std::mutex> interval_locK(interval_mutex_);
					current_interval = request_interval_ms_;
				}

				auto time_since_last = std::chrono::duration_cast<std::chrono::milliseconds>(now - last_request_time_).count();

				if (time_since_last < current_interval) {
					DWORD sleep_time = current_interval - static_cast<DWORD>(time_since_last);
					std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
				}

				//ִ������
				last_request_time_ = std::chrono::steady_clock::now();
				ExecuteRequest(item);
			}
		}

		void RequestQueue::ExecuteRequest(const QueueItem& item) {
			current_concurrency_++;

			std::thread([this, item]() {
				try {
					HttpResponse response = http_client_.SendRequest(item.request);
					if (item.callback) {
						item.callback(response);
					}
				}
				catch (const std::bad_exception& e) {
					if (error_callback_) {
						error_callback_(L"Request execution error:" + std::wstring(e.what(), e.what() + strlen(e.what())));
					}
				}
				current_concurrency_--;
				}).detach();
		}

		void RequestQueue::SetRequestIntervalDynamic(DWORD interval_ms) {
			std::lock_guard<std::mutex> lock(interval_mutex_);
			request_interval_ms_ = (std::max)(100UL, interval_ms);		//��С���100ms
		}

		DWORD RequestQueue::GetCurrentInterval() const {
			std::lock_guard<std::mutex> lock(interval_mutex_);
			return request_interval_ms_;
		}

	} //namespace http
}// namespace chris