#pragma once
#include <UiLib.h>
#include <commctrl.h>
#include "resource.h"
#include "SearchEditUI.h"
#include "FriendsListUI.h"
#include "InitializationManager.h"
#include <vector>
#include <string>

using namespace DuiLib;


//������Ϣ�ṹ��
struct FriendInfo {
	std::wstring name;
	std::wstring status;
	std::wstring signature;
	std::wstring group;
	bool isOnline;
};


class MainFrame :public WindowImplBase,public DuiLib::ISearchEditUICallback,public DuiLib::IFriendsListCallback
{
public:
	MainFrame();
	~MainFrame();

	//ʵ��ISearchEditUICallback�ӿ�
	virtual void OnSearchEditTextChanged(const CDuiString& sText);
	//ʵ��IFriendsListCallback�ӿ�
	virtual void OnFriendsSelected(const CDuiString& sId);
	virtual void OnLoadMoreFriends(int nStartIndex, int nCount);

public:
	LPCTSTR GetWindowClassName() const override { return _T("ChrisMainFrame"); }
	UILIB_RESOURCETYPE GetResourceType() const override {
		return UILIB_RESOURCE;		//��ʾ����Դ�ļ�����
	}
	LPCTSTR GetResourceID() const override {
		return MAKEINTRESOURCE(IDR_SKIN_XML_MAIN_FRAME);		//������ԴID
	}
	CDuiString GetSkinFile() override { return _T("101"); }
	CDuiString GetSkinFolder() override { return _T(""); }

	CControlUI* CreateControl(LPCTSTR pstrClass) override { 
		if (_tcscmp(pstrClass, _T("SearchEdit")) == 0)
			return new CSearchEditUI;
		else if (_tcscmp(pstrClass, _T("FriendItem")) == 0)
			return new CFriendItemUI;
		else if (_tcscmp(pstrClass, _T("FriendsList")) == 0)
			return new CFriendsListUI;
		return NULL; 
	}

	void InitWindow() override;
	void Notify(TNotifyUI& msg) override;

	LRESULT OnClose(UINT uMsg, WPARAM wParam, LPARAM lParam, BOOL& bHandled);
	LRESULT HandleMessage(UINT uMsg, WPARAM wParam, LPARAM lParam) override;



protected:
	void OnFinalMessage(HWND hWnd) override { delete this; }
	void SwitchMaxButtonIcon(bool bMaxImized);
	void SwitchTopButtonState(bool bTopMost);
private:

	static LRESULT CALLBACK SubclassProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam, UINT_PTR uIdSubclass, DWORD_PTR dwRefData);

	bool m_bStartMove;             //�Ƿ�ʼ�ƶ�
	POINT m_StartPt;               //��¼�϶���ʼλ��

	bool m_bIsMaxImized;           //�Ƿ��Ѿ����
	RECT m_rcNormalPostion;        //����״̬�µĴ���λ�úʹ�С

	bool m_bIsTopMost;				//�Ƿ��Ѿ��ö�

	

	//���Ժ����б�����
	void InitTestFriendData();
	std::vector<FriendItemInfo> m_testFriends;		//�����б�
};