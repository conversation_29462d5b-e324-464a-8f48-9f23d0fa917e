#pragma once
#include "RequestQueue.h"
#include <functional>
#include <algorithm>

namespace chris {
	namespace http {
		//΢��API��Ӧ�ص�
		using WeChatApiCallback = std::function<void(bool success, const std::wstring& response, const std::wstring& error)>;
		

		//API��������ö��
		enum class ApiRequestType {
			QUERY,        //��ѯ�����󣨻�ȡ��Ϣ��
			SEND,		 //���������󣨷�����Ϣ,�ļ��ȣ�
			MANAGE,      //������������ӳ�Ա���޸����õȣ�	
		};


		class WeChatApiClient {
		public:

			WeChatApiClient();
			~WeChatApiClient();

			//��ʼ��
			void Initialize(const std::wstring& base_url = L"http://127.0.0.1:19088");

			//����ֹͣ
			void Start();
			void Stop();

			//���ò�ͬ���͵�����ļ��ʱ�䣨��λ�����룩
			void SetQueryInterval(DWORD interval_ms);		//��ѯ��������
			void SetSendinterval(DWORD interval_ms);		//������������	
			void SetManageInterval(DWORD interval_ms);		//������������

			//����API����(��ѯ��-�϶̼��)
			void CheckLogin(WeChatApiCallback callback);
			void GetUserInfo(WeChatApiCallback callback);
			void GetContactList(WeChatApiCallback callback);


			//��Ϣ����API�������� - �ϳ����,��ֹ��ţ�
			void SendTextMessage(const std::wstring& wxid, const std::wstring& message, WeChatApiCallback callback);
			void SendImageMessage(const std::wstring& wxid, const std::wstring& image_path, WeChatApiCallback callback);
			void SendFileMessage(const std::wstring& wxid, const std::wstring& file_path, WeChatApiCallback callback);
			void SendAtMessage(const std::wstring& room_id, const std::vector<std::wstring>& wxids, const std::wstring& message, WeChatApiCallback callback);
			void SendPatMessage(const std::wstring& receiver, const std::wstring& wxid, WeChatApiCallback callback);
			void ForwardMessage(const std::wstring& wxid, int64_t msg_id, WeChatApiCallback callback);


			//Ⱥ��API(��ѯ��͹������ϣ�
			void GetChatRoomInfo(const std::wstring& room_id, WeChatApiCallback callback);
			void GetChatRoomMembers(const std::wstring& room_id, WeChatApiCallback callback);
			void AddMemberToChatRoom(const std::wstring& room_id, const std::vector<std::wstring>& membdr_ids, WeChatApiCallback callback);
			void RemoveMemberFromChatRoom(const std::wstring& room_id, const std::vector<std::wstring>& member_ids, WeChatApiCallback callback);
			void CreateChatRoom(const std::vector<std::wstring>& member_ids, WeChatApiCallback callback);
			void QuitChatRoom(const std::wstring& room_id, WeChatApiCallback callback);



			//��ϵ��API(��ѯ��͹������ϣ�
			void GetContactProfile(const std::wstring& wxid, WeChatApiCallback callback);
			void SearchContact(const std::wstring& keyword, WeChatApiCallback callback);
			void AddContact(const std::wstring& wxid, const std::wstring& message, WeChatApiCallback callback);
			void DeleteContact(const std::wstring& wxid, WeChatApiCallback callback);
			void VerifyApply(const std::wstring& v3, const std::wstring& v4, int permission, WeChatApiCallback callback);

			//���ݿ�API(��ѯ�ࣩ
			void GetDatabaseInfo(WeChatApiCallback callback);
			void ExecuteSQL(int64_t db_handle, const std::wstring& sql, WeChatApiCallback callback);

			//Hook API
			void EnableMessageHook(WeChatApiCallback callback);
			void DisableMessageHook(WeChatApiCallback callback);

			//��������API
			void GetSNSFirstPage(WeChatApiCallback callback);
			void GetSNSNextPage(int64_t sns_id, WeChatApiCallback callback);
			void DoOCRTask(const std::wstring& image_path, WeChatApiCallback callback);
			void LockWeChat(WeChatApiCallback callback);
			void UnlockWeChat(WeChatApiCallback callback);
			void GetLoginUrl(WeChatApiCallback callback);

			//��ȡ����״̬
			size_t GetQueueSize() const;
			bool IsRunning() const;
			void ClearQueue();

		private:
			void SendApiRequest(const std::wstring& endpoint, const std::wstring& json_data, WeChatApiCallback callback,ApiRequestType type, int priority = 5);
			std::wstring CreateJsonRequest(const std::map<std::wstring, std::wstring>& params);
			void HandleApiResponse(const HttpResponse& response, WeChatApiCallback callback);
			int GetPriorityByType(ApiRequestType type);
			DWORD GetIntervalByType(ApiRequestType type);

		private:
			RequestQueue request_queue_;
			std::wstring base_url_;
			bool initialized_;


			//��ͬ��������ļ��ʱ��
			DWORD query_interval_ms_;
			DWORD send_interval_ms_;
			DWORD manage_interval_ms_;
		};
	}		// namespace http
}			// namespace chris