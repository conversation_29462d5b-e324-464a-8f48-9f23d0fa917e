#pragma once
#include "HttpRequest.h"
#include "HttpResponse.h"
#include <functional>
#include <memory>
#include <wininet.h>

#pragma comment(lib, "wininet.lib")

namespace chris {
	namespace http {
	
		//�ص��������Ͷ���
		using HttpCallback = std::function<void(const HttpResponse&)>;

		class HttpClient {
		public:
			HttpClient();
			~HttpClient();

			//ͬ������
			HttpResponse SendRequest(const HttpRequest& request);

			//�첽����
			void SendRequestAsync(const HttpRequest& request, HttpCallback callback);

			//����ȫ�ֳ�ʱʱ��
			void SetDefaultTimeout(DWORD timeout_ms);

			//����ȫ������ͷ
			void SetDefaultHeader(const std::wstring& key, const std::wstring& value);

			//���ô���
			void SetProxy(const std::wstring& proxy_server, DWORD proxy_port = 8080);

		private:
			//�ڲ�ʵ�ַ���
			HttpResponse ExecuteRequest(const HttpRequest& request);
			std::wstring MethodToString(HttpMethod method);
			std::string WStringToString(const std::wstring& wstr);
			std::wstring StringToWString(const std::string& str);

			//Ĭ������
			DWORD default_timeout_ms_;
			std::map<std::wstring, std::wstring> default_headers_;
			std::wstring proxy_server_;
			DWORD proxy_port_;
			bool use_proxy_;
		};
	
	}	//namespace http
}		//namespace chris