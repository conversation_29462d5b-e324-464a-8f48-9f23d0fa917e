#include <UIlib.h>
#include "MainFrame.h"
#include "SplashScreen.h"
#include "LoginWindow.h"
#include "WeChatManager.h"
#include <memory>


//ȫ��WeChat������
std::shared_ptr<chris::wechat::WeChatManager> g_pWeChatManager;

int APIENTRY WinMain(HINSTANCE hInstance, HINSTANCE /*hPrevInstance*/, LPSTR /*lpCmdLine*/, int nCmdShow) {

	CPaintManagerUI::SetInstance(hInstance);
	CPaintManagerUI::SetResourcePath(CPaintManagerUI::GetInstancePath());
	//������Դ·��Ϊexe����Ŀ¼�µ�Skin�ļ���

	HRESULT Hr = ::CoInitialize(NULL);
	if (FAILED(Hr)) return 0;

	//��ʼ��WeChat������
	g_pWeChatManager = std::make_shared<chris::wechat::WeChatManager>();
	g_pWeChatManager->Initialize(L"http://127.0.0.1:19088");
	


	//��������ʾSplashScreen
	SplashScreen* pSplashScreen = new SplashScreen();
	if (pSplashScreen == NULL) {
		::CoUninitialize();
		return 0;
	}



	
	
	//������ɻص�
	pSplashScreen->SetOnCompleteCallback([]() {

		//��ʼ����ɺ��ȼ���¼״̬
		g_pWeChatManager->Start();

		g_pWeChatManager->CheckLoginStatus([](chris::wechat::LoginStatus status, const std::wstring& error) {
			if (status == chris::wechat::LoginStatus::LOGGED_IN) {
			//�ѵ�¼��ֱ�Ӵ���������
				MainFrame* pFrame = new MainFrame();
				if (pFrame == NULL) return;

				pFrame->SetWeChatManager(g_pWeChatManager);
				pFrame->Create(NULL, _T("Chris"), UI_WNDSTYLE_FRAME | WS_THICKFRAME, WS_EX_WINDOWEDGE);
				pFrame->CenterWindow();
				pFrame->ShowWindow(true);
			}
			else {
				//δ��¼����ʾ��¼����
				LoginWindow* pLoginWindow = new LoginWindow();
				if (pLoginWindow == NULL)return;

				pLoginWindow->SetWeChatManager(g_pWeChatManager);
				pLoginWindow->SetOnLoginSuccessCallback([]() {
					//��¼�ɹ��󴴽�������
					MainFrame* pFrame = new MainFrame();
					if (pFrame == NULL) return;

					pFrame->SetWeChatManager(g_pWeChatManager);
					pFrame->Create(NULL, _T("Chris"), UI_WNDSTYLE_FRAME | WS_THICKFRAME, WS_EX_WINDOWEDGE);
					pFrame->CenterWindow();
					pFrame->ShowWindow(true);
					});

				pLoginWindow->Create(NULL, _T("Chris - ��¼"), UI_WNDSTYLE_DIALOG, WS_EX_STATICEDGE | WS_EX_APPWINDOW, 0, 0, 400, 500);
				pLoginWindow->CenterWindow();
				pLoginWindow->ShowWindow();
			}
		});

		//��ʼ����ɺ󴴽�������
		//MainFrame* pFrame = new MainFrame();
		//if (pFrame == NULL) return;

		//pFrame->Create(NULL, _T("Chris"), UI_WNDSTYLE_FRAME | WS_THICKFRAME, WS_EX_WINDOWEDGE);
		//pFrame->CenterWindow();
		//pFrame->ShowWindow(true);

		});

	//��������ʾSplashScreen
	pSplashScreen->Create(NULL, _T("Chris - ������"), UI_WNDSTYLE_DIALOG, WS_EX_STATICEDGE | WS_EX_APPWINDOW, 0, 0, 400, 300);
	pSplashScreen->CenterWindow();
	pSplashScreen->ShowWindow();

	CPaintManagerUI::MessageLoop();

	::CoUninitialize();
	return 0;
}