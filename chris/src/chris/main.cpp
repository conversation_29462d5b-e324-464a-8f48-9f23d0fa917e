#include <UIlib.h>
#include "MainFrame.h"
#include "SplashScreen.h"
#include "LoginWindow.h"
#include "WeChatManager.h"
#include <memory>


//ȫ��WeChat������
std::shared_ptr<chris::wechat::WeChatManager> g_pWeChatManager;

int APIENTRY WinMain(HINSTANCE hInstance, HINSTANCE /*hPrevInstance*/, LPSTR /*lpCmdLine*/, int nCmdShow) {

	CPaintManagerUI::SetInstance(hInstance);
	CPaintManagerUI::SetResourcePath(CPaintManagerUI::GetInstancePath());

	HRESULT Hr = ::CoInitialize(NULL);
	if (FAILED(Hr)) return 0;

	//��ʼ��WeChat������
	g_pWeChatManager = std::make_shared<chris::wechat::WeChatManager>();
	g_pWeChatManager->Initialize(L"http://127.0.0.1:19088");

	//������ʾSplashScreen
	SplashScreen* pSplashScreen = new SplashScreen();
	if (pSplashScreen == NULL) {
		::CoUninitialize();
		return 0;
	}


	//������ɻص�
	pSplashScreen->SetOnCompleteCallback([]() {
		//��ʼ����ɺ��ȼ���¼״̬
		g_pWeChatManager->Start();

		//ʹ��PostMessageȷ����UI�߳���ִ��
		HWND hMainWnd = ::GetActiveWindow();
		::PostMessage(hMainWnd, WM_USER + 100, 0, 0);//�Զ�����Ϣ����ʾSplashScreen��ɺ���Ҫִ�еĲ���

		});

	//��������ʾSplashScreen����
	pSplashScreen->Create(NULL, _T("Chris - ��ʼ��"), UI_WNDSTYLE_DIALOG, WS_EX_STATICEDGE | WS_EX_APPWINDOW, 0, 0, 400, 300);
	pSplashScreen->CenterWindow();
	pSplashScreen->ShowWindow();

	//�Զ�����Ϣ����
	MSG msg;
	while (GetMessage(&msg, NULL, 0, 0)) {
		if (msg.message == WM_USER + 100) {
			//��UI�߳��м���¼״̬
			g_pWeChatManager->CheckLoginStatus([](chris::wechat::LoginStatus status, const std::wstring& error) {
				//ʹ��PostMessage��������͵�UI�߳�
				HWND hMainWnd = ::GetActiveWindow();
				if (status == chris::wechat::LoginStatus::LOGGED_IN) {
					::PostMessage(hMainWnd, WM_USER + 101, 1, 0);		//�ѵ�¼
				}
				else {
					::PostMessage(hMainWnd, WM_USER + 101, 0, 0);//δ��¼
				}

				});
		}
		else if (msg.message == WM_USER + 101) {
		//��UI�߳��д�������
			if (msg.wParam == 1) {
				//�ѵ�¼������������
				MainFrame* pFrame = new MainFrame();
				if (pFrame != NULL) {
					pFrame->SetWeChatManager(g_pWeChatManager);
					pFrame->Create(NULL, _T("Chris"), UI_WNDSTYLE_FRAME | WS_THICKFRAME, WS_EX_WINDOWEDGE);
					pFrame->CenterWindow();
					pFrame->ShowWindow(true);
				}
			}
			else {
				//δ��¼����ʾ��¼����
				LoginWindow* pLoginWindow = new LoginWindow();
				if (pLoginWindow != NULL) {
					pLoginWindow->SetWeChatManager(g_pWeChatManager);
					pLoginWindow->SetOnLoginSuccessCallback([]() {
						//��¼�ɹ��󣬴���������
						MainFrame* pFrame = new MainFrame();
						if (pFrame != NULL) {
							pFrame->SetWeChatManager(g_pWeChatManager);
							pFrame->Create(NULL, _T("Chris"), UI_WNDSTYLE_FRAME | WS_THICKFRAME, WS_EX_WINDOWEDGE);
							pFrame->CenterWindow();
							pFrame->ShowWindow(true);
						}
					});

					pLoginWindow->Create(NULL, _T("Chris - ��¼"), UI_WNDSTYLE_DIALOG, WS_EX_STATICEDGE | WS_EX_APPWINDOW, 0, 0, 400, 300);
					pLoginWindow->CenterWindow();
					pLoginWindow->ShowWindow(true);
				}
			}
		}
		else {
			TranslateMessage(&msg);
			DispatchMessage(&msg);
		}
	}

	::CoUninitialize();
	return 0;
}