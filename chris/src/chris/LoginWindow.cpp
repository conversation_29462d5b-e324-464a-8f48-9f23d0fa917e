#include "LoginWindow.h"
#include <thread>


LoginWindow::LoginWindow() :
	m_pStatusLabel(nullptr), m_pQRCodeControl(nullptr), m_pRefreshButton(nullptr),
	m_bLoginProcessStarted(false) {
}


LoginWindow::~LoginWindow() {
	if (m_pWeChatManager) {
		m_pWeChatManager->StopLoginMonitoring();
	}
}

void LoginWindow::SetOnLoginSuccessCallback(std::function<void()> callback) {
	m_onLoginSuccess = callback;
}

void LoginWindow::SetWeChatManager(std::shared_ptr<chris::wechat::WeChatManager> manager) {
	m_pWeChatManager = manager;
}

void LoginWindow::InitWindow() {
	//��ȡ�ؼ�
	m_pStatusLabel = static_cast<CLabelUI*>(m_PaintManager.FindControl(_T("statusLabel")));
	m_pQRCodeControl = static_cast<CControlUI*>(m_PaintManager.FindControl(_T("qrCodeImage")));
	m_pRefreshButton = static_cast<CButtonUI*>(m_PaintManager.FindControl(_T("refreshButton")));

	//���ó�ʼ״̬
	if (m_pStatusLabel) {
		m_pStatusLabel->SetText(_T("���ڼ���¼״̬..."));
	}

	//������ʾ
	CenterWindow();

	//���ô����ö�
	::SetWindowPos(m_hWnd, HWND_TOPMOST, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE);

	//��ʼ��¼����
	StartLoginProcess();

}


void LoginWindow::Notify(TNotifyUI& msg) {
	if (msg.sType == _T("click")) {
		if (msg.pSender->GetName() == _T("refreshButton")) {
			//ˢ�¶�ά��
			StartLoginProcess();
		}
		else if (msg.pSender->GetName() == _T("closeButton")) {
			//�رմ���
			Close();
		}
	}
}


LRESULT LoginWindow::HandleMessage(UINT uMsg, WPARAM wParam, LPARAM lParam) {
	switch (uMsg) {
	case WM_USER + 10://����״̬�ı�
	{
		CDuiString* pText = reinterpret_cast<CDuiString*>(wParam);
		if (pText && m_pStatusLabel) {
			m_pStatusLabel->SetText(*pText);
			delete pText;
		}
	}
	return 0;

	case WM_USER + 11://���¶�ά��
	{
		std::wstring* pUrl = reinterpret_cast<std::wstring*>(wParam);
		if (pUrl) {
			UpdateQRCode(*pUrl);
			delete pUrl;
		}
	}
	return 0;


	case WM_USER + 12://��¼�ɹ�
	{
		if (m_onLoginSuccess) {
			m_onLoginSuccess();
		}
		Close();
	}
	return 0;

	default:
		break;
	}

	return WindowImplBase::HandleMessage(uMsg, wParam, lParam);
}


void LoginWindow::UpdateStatusText(const CDuiString& text) {
	//�̰߳�ȫ��״̬�ı�����
	CDuiString* pText = new CDuiString(text);
	::PostMessage(m_hWnd, WM_USER + 10, reinterpret_cast<WPARAM>(pText), 0);
}

void LoginWindow::UpdateQRCode(const std::wstring& qr_url) {
	if (m_pQRCodeControl && !qr_url.empty()) {
		//�������ʹ�ö�ά�����ɿ����ɶ�ά��ͼƬ
		//������ʾ��ά��URL���û��ֶ�ɨ��
		//��ʱ��ʾURL�ı�
		CDuiString qr_text = _T("��ʹ��΢��ɨ���ά���¼:\n");
		qr_text += qr_url.c_str();

		//����ؼ��� Label��ֱ�������ı�
		if (CLabelUI* pLabel = dynamic_cast<CLabelUI*>(m_pQRCodeControl)) {
			pLabel->SetText(qr_text);
		}
	}
}

void LoginWindow::ShowError(const std::wstring& error) {
	UpdateStatusText(CDuiString(error.c_str()));
}


void LoginWindow::StartLoginProcess() {
	if (!m_pWeChatManager) {
		ShowError(L"WeChat������δ��ʼ��");
		return;
	}

	UpdateStatusText(_T("���ڼ���¼״̬..."));

	//���ȼ�鵱ǰ��¼״̬
	m_pWeChatManager->CheckLoginStatus([this](chris::wechat::LoginStatus status, const std::wstring& error) {
		switch (status) {
		case chris::wechat::LoginStatus::LOGGED_IN:
			//�ѵ�¼��ֱ����ת��������
			::PostMessage(m_hWnd, WM_USER + 12, 0, 0);
			break;

		case chris::wechat::LoginStatus::NOT_LOGGED_IN:
			//δ��¼����ȡ��ά��
			UpdateStatusText(_T("���ڻ�ȡ��¼��ά��..."));
			m_pWeChatManager->GetLoginQRCode([this](bool success, const std::wstring& qr_url, const std::wstring& error) {
				OnQRCodeReceived(success, qr_url, error);
				});
			break;

		default:
			//״̬δ֪����ʧ��
			ShowError(L"����¼״̬ʧ��:" + error);
			break;
		}

		});
}



void LoginWindow::OnLoginStatusChanged(chris::wechat::LoginStatus status, const std::wstring& error) {
	switch (status) {
	case chris::wechat::LoginStatus::LOGGED_IN:
		UpdateStatusText(_T("��¼�ɹ���������ת..."));
		//�ӳ�һ������ת�����û������ɹ���Ϣ
		std::thread([this]() {
			std::this_thread::sleep_for(std::chrono::milliseconds(1000));
			::PostMessage(m_hWnd, WM_USER + 12, 0, 0);
			}).detach();
		break;

	case chris::wechat::LoginStatus::NOT_LOGGED_IN:
		UpdateStatusText(_T("�ȴ�ɨ���¼..."));
		break;

	case chris::wechat::LoginStatus::CHECKING:
		UpdateStatusText(_T("���ڼ���¼״̬..."));
		break;

	default:
		ShowError(L"��¼״̬���ʧ��:" + error);
		break;
	}
}

