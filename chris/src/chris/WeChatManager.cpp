#include "WeChatManager.h"
#include <thread>

namespace chris {
	namespace wechat {
		WeChatManager::WeChatManager() :
			current_status_(LoginStatus::UNKNOWN), monitoring_active_(false),
			check_interval_ms_(2000), initialized_(false) {
			api_client_ = std::make_unique<chris::http::WeChatApiClient>();
		}

		WeChatManager::~WeChatManager() {
			Stop();
		}

		void WeChatManager::Initialize(const std::wstring& api_base_url) {
			api_client_->Initialize(api_base_url);

			//����API������
			api_client_->SetQueryInterval(500);		//��ѯ��������500ms
			api_client_->SetSendinterval(2000);		//������������2000ms
			api_client_->SetManageInterval(1000);	//������1����

			initialized_ = true;
		}


		void WeChatManager::Start() {
			if (!initialized_) {
				return;
			}
			api_client_->Start();
		}

		void WeChatManager::Stop() {
			StopLoginMonitoring();
			if (api_client_) {
				api_client_->Stop();
			}
		}

		void WeChatManager::CheckLoginStatus(LoginStatusCallback callback) {
			if (!initialized_) {
				if (callback) {
					callback(LoginStatus::UNKNOWN, L"WeChatManager not initialized");
				}
				return;
			}

			{
				std::lock_guard<std::mutex> lock(status_mutex_);
				current_status_ = LoginStatus::CHECKING;
			}

			api_client_->CheckLogin([this, callback](bool success, const std::wstring& response, const std::wstring& error) {
				LoginStatus status = LoginStatus::UNKNOWN;
				std::wstring error_msg = error;

				if (success) {
					//����JSON��Ӧ�е�code�ֶ�
					//�򵥵�JSON����������"code":1��ʾ�ѵ�¼
					size_t code_pos = response.find(L"\"code\":");
					if (code_pos != std::wstring::npos) {
						size_t code_start = code_pos + 7;
						size_t code_end = response.find_first_of(L",}", code_start);
						if (code_end != std::wstring::npos) {
							std::wstring code_str = response.substr(code_start, code_end - code_start);
							code_str.erase(0, code_str.find_first_not_of(L" \t\""));
							code_str.erase(code_str.find_last_not_of(L" \t\"") + 1);

							try {
								int code = std::stoi(code_str);
								status = (code == 1) ? LoginStatus::LOGGED_IN : LoginStatus::NOT_LOGGED_IN;
							}
							catch (...) {
								status = LoginStatus::UNKNOWN;
								error_msg = L"Failed to parse login status";
							}
						}
					}
				}
				else {
					status = LoginStatus::UNKNOWN;
				}

				{
					std::lock_guard<std::mutex> lock(status_mutex_);
					current_status_ = status;
				}

				if (callback) {
					callback(status, error_msg);
				}
				});
		}

		void WeChatManager::StartLoginMonitoring(LoginStatusCallback callback, DWORD check_interval_ms) {
			StopLoginMonitoring();		//ֹ֮ͣǰ�ļ��

			login_callback_ = callback;
			check_interval_ms_ = check_interval_ms;
			monitoring_active_.store(true);

			monitoring_thread_ = std::thread(&WeChatManager::LoginMonitoringLoop, this);

		}

		void WeChatManager::StopLoginMonitoring() {
			monitoring_active_.store(false);
			if (monitoring_thread_.joinable()) {
				monitoring_thread_.join();
			}
		}

		void WeChatManager::GetLoginQRCode(QRCodeCallback callback) {
			if (!initialized_) {
				if (callback) {
					callback(false, L"", L"WeChatManager not initialized");
				}
				return;
			}

			api_client_->GetLoginUrl([callback](bool success, const std::wstring& response, const std::wstring& error) {

				if (success && callback) {
					//����JSON��Ӧ�е�loginUrl�ֶ�
					size_t url_pos = response.find(L"\"loginUrl\":");
					if (url_pos != std::wstring::npos) {
						size_t url_start = response.find(L"\"", url_pos + 11) + 1;
						size_t url_end = response.find(L"\"", url_start);
						if (url_start != std::wstring::npos && url_end != std::wstring::npos) {
							std::wstring qr_url = response.substr(url_start, url_end - url_start);
							callback(true, qr_url, L"");
						}
						else {
							callback(false, L"", L"Failed to parse QR code URL");
						}
					}
					else {
						callback(false, L"", L"QR code URL not found in response");
					}
				}
				else if (callback) {
					callback(false, L"", error);
				}
				});
		}


		void WeChatManager::GetUserInfo(UserInfoCallback callback) {
			if (!initialized_) {
				if (callback) {
					callback(false, L"", L"WeChatManager not initialized");
				}
				return;
			}

			api_client_->GetUserInfo(callback);
		}

		LoginStatus WeChatManager::GetCurrentLoginStatus() const {
			std::lock_guard<std::mutex> lock(status_mutex_);
			return current_status_;
		}

		chris::http::WeChatApiClient& WeChatManager::GetApiClient() {
			return *api_client_;
		}

		void WeChatManager::LoginMonitoringLoop() {
			while (monitoring_active_.load()) {
				CheckLoginStatus([this](LoginStatus status, const std::wstring& error) {
					if (login_callback_) {
						login_callback_(status, error);
					}

				});

				//�ȴ�ָ�����ʱ��
				for (DWORD i = 0; i < check_interval_ms_ / 100 && monitoring_active_.load(); ++i) {
					std::this_thread::sleep_for(std::chrono::milliseconds(100));
				}
			}

		}

	}	// namespace wechat
}	// namespace chris