#pragma once
#include <UIlib.h>
#include <Windows.h>
#include <string>
#include <functional>
#include "resource.h"
#include "WeChatManager.h"

using namespace DuiLib;

class LoginWindow :public WindowImplBase {
public:
	LoginWindow();
	~LoginWindow();

	//���õ�¼�ɹ��ص�
	void SetOnLoginSuccessCallback(std::function<void()> callback);

	//����WeChat������
	void SetWeChatManager(std::shared_ptr<chris::wechat::WeChatManager> manager);

public:
	//WindowImplBase�ӿ�ʵ��
	LPCTSTR GetWindowClassName() const override { return _T("ChrisLoginWindow"); }
	UILIB_RESOURCETYPE GetResourceType() const override { return UILIB_RESOURCE; }
	LPCTSTR GetResourceID() const override {return MAKEINTRESOURCE() }//TODO:
};