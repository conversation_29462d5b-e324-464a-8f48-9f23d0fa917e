#pragma once
#include <UIlib.h>
#include <Windows.h>
#include <string>
#include <functional>
#include "resource.h"
#include "WeChatManager.h"

using namespace DuiLib;

class LoginWindow :public WindowImplBase {
public:
	LoginWindow();
	~LoginWindow();

	//���õ�¼�ɹ��ص�
	void SetOnLoginSuccessCallback(std::function<void()> callback);

	//����WeChat������
	void SetWeChatManager(std::shared_ptr<chris::wechat::WeChatManager> manager);

public:
	//WindowImplBase�ӿ�ʵ��
	LPCTSTR GetWindowClassName() const override { return _T("ChrisLoginWindow"); }
	UILIB_RESOURCETYPE GetResourceType() const override { return UILIB_RESOURCE; }
	LPCTSTR GetResourceID() const override { return MAKEINTRESOURCE(IDR_SKIN_XML_LOGIN); }
	CDuiString GetSkinFile() override { return _T("133"); }//������ԴID
	CDuiString GetSkinFolder() override { return _T(""); }

	void InitWindow() override;
	void Notify(TNotifyUI& msg) override;
	LRESULT HandleMessage(UINT uMsg, WPARAM wParam, LPARAM lParam) override;

	//������
	void OnFinalMessage(HWND hWnd) override;

private:
	//������·���
	void UpdateStatusText(const CDuiString& text);
	void UpdateQRCode(const std::wstring& qr_url);
	void ShowError(const std::wstring& error);

	//��¼���̷���
	void StartLoginProcess();
	void OnLoginStatusChanged(chris::wechat::LoginStatus status, const std::wstring& error);
	void OnQRCodeReceived(bool success, const std::wstring& qr_url, const std::wstring& error);

private:
	CLabelUI* m_pStatusLabel; //״̬��ǩ
	CControlUI* m_pQRCodeControl; //��ά��ؼ�
	CButtonUI* m_pRefreshButton; //ˢ�°�ť

	std::function<void()> m_onLoginSuccess;		//��¼�ɹ��ص�����
	std::shared_ptr<chris::wechat::WeChatManager> m_pWeChatManager; //WeChat������ʵ��

	bool m_bLoginProcessStarted;		//��¼�����Ƿ��ѿ�ʼ
};