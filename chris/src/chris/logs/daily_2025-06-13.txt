2025-06-13 22:14:21 [info] [11064] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-13 22:14:22 [info] [11064] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-13 22:14:22 [info] [11064] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-13 22:36:47 [info] [26776] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-13 22:36:48 [info] [26776] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-13 22:36:48 [info] [26776] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-13 22:41:49 [info] [30880] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-13 22:41:50 [info] [30880] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-13 22:41:50 [info] [30880] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-13 22:42:00 [info] [19120] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-13 22:42:01 [info] [19120] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-13 22:42:01 [info] [19120] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-13 22:42:18 [info] [17216] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-13 22:42:19 [info] [17216] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-13 22:42:19 [info] [17216] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-13 22:43:43 [info] [25956] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-13 22:43:43 [info] [25956] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-13 22:43:43 [info] [25956] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-13 22:45:46 [info] [25120] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-13 22:45:47 [info] [25120] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-13 22:45:47 [info] [25120] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-13 22:52:25 [info] [26616] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-13 22:52:25 [info] [26616] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-13 22:52:25 [info] [26616] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-13 22:55:08 [info] [5768] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-13 22:55:09 [info] [5768] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-13 22:55:09 [info] [5768] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-13 23:35:02 [info] [28484] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-13 23:36:40 [info] [28484] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-13 23:36:40 [info] [28484] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-13 23:55:17 [info] [2524] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-13 23:55:18 [info] [2524] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-13 23:55:18 [info] [2524] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-13 23:55:18 [info] [29220] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-13 23:55:56 [info] [29220] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-13 23:58:47 [info] [29220] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
