2025-06-14 00:16:55 [info] [30896] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 00:18:24 [info] [30896] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 00:18:24 [info] [30896] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-14 00:18:24 [info] [31968] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 00:20:49 [info] [30472] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 00:20:50 [info] [30472] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 00:20:50 [info] [30472] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-14 00:21:00 [info] [33012] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 00:21:00 [info] [33012] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 00:21:00 [info] [33012] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-14 00:21:00 [info] [28024] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 00:21:17 [info] [17988] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 00:22:10 [info] [17988] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 00:22:10 [info] [17988] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-14 00:22:35 [info] [3832] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 00:22:35 [info] [3832] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 00:22:35 [info] [3832] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-14 00:22:36 [info] [13000] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 00:22:38 [info] [13000] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 00:24:57 [info] [17704] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 00:25:59 [info] [17704] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 00:25:59 [info] [17704] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-14 00:26:10 [info] [23204] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 00:26:22 [info] [23204] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 00:33:26 [info] [13896] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 00:33:27 [info] [13896] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 00:33:27 [info] [13896] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-14 00:33:35 [info] [5596] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 00:33:39 [info] [5596] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:03:10 [info] [46668] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 04:03:11 [info] [46668] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 04:03:11 [info] [46668] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
