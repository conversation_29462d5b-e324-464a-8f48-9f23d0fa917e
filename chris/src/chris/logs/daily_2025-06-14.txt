2025-06-14 00:16:55 [info] [30896] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 00:18:24 [info] [30896] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 00:18:24 [info] [30896] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-14 00:18:24 [info] [31968] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 00:20:49 [info] [30472] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 00:20:50 [info] [30472] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 00:20:50 [info] [30472] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-14 00:21:00 [info] [33012] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 00:21:00 [info] [33012] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 00:21:00 [info] [33012] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-14 00:21:00 [info] [28024] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 00:21:17 [info] [17988] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 00:22:10 [info] [17988] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 00:22:10 [info] [17988] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-14 00:22:35 [info] [3832] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 00:22:35 [info] [3832] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 00:22:35 [info] [3832] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-14 00:22:36 [info] [13000] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 00:22:38 [info] [13000] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 00:24:57 [info] [17704] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 00:25:59 [info] [17704] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 00:25:59 [info] [17704] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-14 00:26:10 [info] [23204] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 00:26:22 [info] [23204] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 00:33:26 [info] [13896] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 00:33:27 [info] [13896] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 00:33:27 [info] [13896] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-14 00:33:35 [info] [5596] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 00:33:39 [info] [5596] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:03:10 [info] [46668] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 04:03:11 [info] [46668] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 04:03:11 [info] [46668] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-14 04:04:49 [info] [32376] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 04:04:50 [info] [32376] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 04:04:50 [info] [32376] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-14 04:04:50 [info] [45716] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:04:50 [info] [45716] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:06:13 [info] [45716] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:06:45 [info] [45716] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:20:20 [info] [35016] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 04:20:21 [info] [35016] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 04:20:21 [info] [35016] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-14 04:20:21 [info] [7208] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:20:21 [info] [7208] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:20:50 [info] [7208] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:20:56 [info] [7208] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:20:58 [info] [7208] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:21:00 [info] [7208] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:21:02 [info] [7208] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:21:04 [info] [7208] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:21:07 [info] [7208] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:21:09 [info] [7208] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:21:32 [info] [7208] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:21:32 [info] [7208] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:21:49 [info] [31248] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 04:21:50 [info] [31248] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 04:21:50 [info] [31248] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-14 04:21:50 [info] [4944] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:21:50 [info] [4944] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:21:51 [info] [4944] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:23:42 [info] [4944] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:23:44 [info] [4944] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:23:47 [info] [4944] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:23:49 [info] [4944] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:23:51 [info] [4944] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:23:53 [info] [4944] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:23:55 [info] [4944] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:23:57 [info] [4944] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:23:59 [info] [4944] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:24:02 [info] [4944] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:24:04 [info] [4944] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:24:06 [info] [4944] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:24:08 [info] [4944] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:24:23 [info] [4944] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:24:38 [info] [40788] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 04:24:38 [info] [40788] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 04:24:38 [info] [40788] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-14 04:24:38 [info] [36936] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:25:06 [info] [36936] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:25:47 [info] [36936] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:26:20 [info] [36936] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:26:22 [info] [36936] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:26:24 [info] [36936] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:26:26 [info] [36936] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:26:28 [info] [36936] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:26:31 [info] [36936] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:27:12 [info] [35784] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 04:27:13 [info] [35784] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 04:27:13 [info] [35784] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-14 04:27:14 [info] [84] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:27:14 [info] [84] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:28:10 [info] [39312] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 04:28:11 [info] [39312] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 04:28:11 [info] [39312] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-14 04:28:11 [info] [35796] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:28:11 [info] [35796] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:28:51 [info] [35796] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:29:52 [info] [35796] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:29:54 [info] [35796] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:29:56 [info] [35796] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:29:58 [info] [35796] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 04:30:01 [info] [35796] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 11:17:44 [info] [21960] - <config.cpp>|<72>|<wxhelper::Config::init>,wxhelper config:HttpServerProt=19088,HttpServerHost=http://0.0.0.0,RecvMessageMode=tcp,RecvTcpIp=127.0.0.1,RecvTcpPort=19099,RecvHttpUrl=127.0.0.1,RecvHttpTimeout=3000,RecvFakeWechatVersion=********
2025-06-14 11:17:45 [info] [21960] - <http_server.cpp>|<26>|<http::HttpServer::init>,Http init:host=http://0.0.0.0,port=19088
2025-06-14 11:17:45 [info] [21960] - <http_server.cpp>|<39>|<http::HttpServer::Start>,CreateThread for http server,the result istrue
2025-06-14 11:17:52 [info] [22188] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 11:18:22 [info] [22188] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 11:18:30 [info] [22188] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 11:18:32 [info] [22188] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
2025-06-14 11:18:34 [info] [22188] - <http_router.cpp>|<15>|<http::HttpRouter::HandleHttpRequest>,route table size=53
