  HttpClient.cpp
  RequestQueue.cpp
F:\Code\test\injector-2\chris\src\chris\HttpClient.cpp(130,27): warning C4267: “参数”: 从“size_t”转换到“DWORD”，可能丢失数据
F:\Code\test\injector-2\chris\src\chris\HttpClient.cpp(137,22): warning C4267: “参数”: 从“size_t”转换到“DWORD”，可能丢失数据
F:\Code\test\injector-2\chris\src\chris\HttpClient.cpp(156,35): error C2065: “bytes_raed”: 未声明的标识符
F:\Code\test\injector-2\chris\src\chris\RequestQueue.cpp(58,32): error C2589: “(”:“::”右边的非法标记
F:\Code\test\injector-2\chris\src\chris\RequestQueue.cpp(58,32): error C2059: 语法错误:“)”
F:\Code\test\injector-2\chris\src\chris\RequestQueue.cpp(62,28): error C2589: “(”:“::”右边的非法标记
F:\Code\test\injector-2\chris\src\chris\RequestQueue.cpp(62,28): error C2059: 语法错误:“)”
F:\Code\test\injector-2\chris\src\chris\RequestQueue.cpp(166,32): error C2589: “(”:“::”右边的非法标记
F:\Code\test\injector-2\chris\src\chris\RequestQueue.cpp(166,32): error C2059: 语法错误:“)”
  WeChatApiClient.cpp
F:\Code\test\injector-2\chris\src\chris\WeChatApiClient.cpp(37,30): error C2589: “(”:“::”右边的非法标记
F:\Code\test\injector-2\chris\src\chris\WeChatApiClient.cpp(37,30): error C2059: 语法错误:“)”
F:\Code\test\injector-2\chris\src\chris\WeChatApiClient.cpp(41,29): error C2589: “(”:“::”右边的非法标记
F:\Code\test\injector-2\chris\src\chris\WeChatApiClient.cpp(41,29): error C2059: 语法错误:“)”
F:\Code\test\injector-2\chris\src\chris\WeChatApiClient.cpp(45,31): error C2589: “(”:“::”右边的非法标记
F:\Code\test\injector-2\chris\src\chris\WeChatApiClient.cpp(45,31): error C2059: 语法错误:“)”
  正在生成代码...
