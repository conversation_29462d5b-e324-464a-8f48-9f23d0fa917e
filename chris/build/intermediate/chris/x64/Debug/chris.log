  main.cpp
F:\Code\test\injector-2\chris\third_party\duilib\include\Core\UIManager.h(16,14): warning C4091: “typedef ”: 没有声明变量时忽略“DuiLib::EVENTTYPE_UI”的左侧
  (编译源文件“main.cpp”)
  
C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.42.34433\include\filesystem(12): warning STL4038: The contents of <filesystem> are available only with C++17 or later.
  chris.vcxproj -> F:\Code\test\injector-2\chris\build\x64\Debug\chris.exe
  'pwsh.exe' 不是内部或外部命令，也不是可运行的程序
  或批处理文件。
