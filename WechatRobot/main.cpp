#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#ifndef NOMINMAX
#define NOMINMAX
#endif

#include <WinSock2.h>
#include <Windows.h>
#include "Injector.h"
#include "tcp_server.h"
#include <iostream>
#include <filesystem>
#include <thread>
#include <chrono>
#include <string>

// 处理从DLL接收到的消息
void handle_message(const Message& msg) {
    std::wcout << L"收到消息：" << std::endl;
    std::wcout << L"发送者: " << msg.sender_id << std::endl;
    std::wcout << L"接收者: " << msg.receiver_id << std::endl;
    std::wcout << L"内容: " << msg.content << std::endl;
    std::wcout << L"类型: " << static_cast<int>(msg.type) << std::endl;
}

// 显示帮助信息
void show_help() {
    std::cout << "\n可用命令：" << std::endl;
    std::cout << "1. send <接收者ID> <消息内容> - 发送文本消息" << std::endl;
    std::cout << "2. help - 显示此帮助信息" << std::endl;
    std::cout << "3. exit - 退出程序" << std::endl;
}

// 处理用户输入的命令
void process_command(const std::string& cmd, TcpServer& server) {
    std::istringstream iss(cmd);
    std::string command;
    iss >> command;

    if (command == "send") {
        std::string receiver_id, content;
        iss >> receiver_id;
        
        // 读取剩余所有内容作为消息内容
        std::getline(iss, content);
        if (!content.empty() && content[0] == ' ') {
            content = content.substr(1);  // 删除开头的空格
        }

        if (receiver_id.empty() || content.empty()) {
            std::cout << "用法: send <接收者ID> <消息内容>" << std::endl;
            return;
        }

        Message msg;
        msg.sender_id = L"WechatRobot";  // 发送者ID设为WechatRobot
        msg.receiver_id = std::wstring(receiver_id.begin(), receiver_id.end());
        msg.content = std::wstring(content.begin(), content.end());
        msg.type = MessageType::Text;

        server.send_message(msg);
        std::cout << "消息已发送" << std::endl;
    }
    else if (command == "help") {
        show_help();
    }
    else if (command == "exit") {
        std::cout << "正在退出..." << std::endl;
        exit(0);
    }
    else {
        std::cout << "未知命令，输入 'help' 查看可用命令" << std::endl;
    }
}

// 启动WeChat并等待其完全加载
bool LaunchWeChat(const std::wstring& wechatPath) {
    STARTUPINFOW si = { sizeof(STARTUPINFOW) };
    PROCESS_INFORMATION pi = { 0 };

    // 创建WeChat进程
    if (!CreateProcessW(
        wechatPath.c_str(),    // 应用程序路径
        nullptr,               // 命令行参数
        nullptr,               // 进程安全属性
        nullptr,               // 线程安全属性
        FALSE,                // 不继承句柄
        0,                    // 创建标志
        nullptr,               // 环境变量
        nullptr,               // 当前目录
        &si,                  // 启动信息
        &pi                   // 进程信息
    )) {
        std::cout << "启动WeChat失败！错误代码: " << GetLastError() << std::endl;
        return false;
    }

    // 关闭进程和线程句柄
    CloseHandle(pi.hThread);
    CloseHandle(pi.hProcess);

    std::cout << "正在等待WeChat启动..." << std::endl;

    // 等待WeChat完全启动
    int retries = 0;
    while (retries < 30) { // 最多等待30秒
        if (Injector::GetProcessIdByName(L"WeChat.exe") != 0) {
            std::this_thread::sleep_for(std::chrono::seconds(2)); // 额外等待2秒确保完全加载
            return true;
        }
        std::this_thread::sleep_for(std::chrono::seconds(1));
        retries++;
    }

    std::cout << "等待WeChat启动超时！" << std::endl;
    return false;
}

int main() {
    try {
        // 设置控制台以支持宽字符输出
        setlocale(LC_ALL, "");

        // 获取当前目录
        std::wstring currentDir = Injector::GetCurrentDirectory();
        std::wstring dllPath = currentDir + L"WechatDll.dll";

        // 检查DLL文件是否存在
        if (!std::filesystem::exists(dllPath)) {
            throw std::runtime_error("在当前目录下未找到WechatDll.dll");
        }

        // 获取WeChat路径并检查进程
        std::wstring wechatPath = Injector::GetWeChatPath();
        std::wstring processName = L"WeChat.exe";

        //检查WeChat是否已经在运行
        if (Injector::GetProcessIdByName(processName) != 0) {
            std::cout << "检测到WeChat已经在运行" << std::endl;
        }
        else {
            std::cout << "正在启动WeChat..." << std::endl;
            if (!LaunchWeChat(wechatPath)) {
                throw std::runtime_error("无法启动WeChat");
            }
            std::cout << "WeChat启动成功" << std::endl;
        }

        // 注入DLL
        if (Injector::InjectDll(processName, dllPath)) {
            std::cout << "DLL注入成功！" << std::endl;
            std::cout << "WeChatRobot正在运行中..." << std::endl;
            std::cout << "等待与DLL建立通信..." << std::endl;

            // 创建并启动TCP服务器
            boost::asio::io_context io_context;
            TcpServer server(io_context, 6666);  // 使用6666端口
            server.set_message_handler(handle_message);
            server.start();

            // 创建一个线程运行io_context
            std::thread io_thread([&io_context]() {
                io_context.run();
            });

            // 显示帮助信息
            show_help();

            // 主循环处理用户输入
            std::string cmd;
            while (true) {
                std::cout << "\n> ";
                std::getline(std::cin, cmd);
                if (!cmd.empty()) {
                    process_command(cmd, server);
                }
            }

            // 清理
            io_context.stop();
            if (io_thread.joinable()) {
                io_thread.join();
            }
        }
        else {
            std::cout << "DLL注入失败！" << std::endl;
            return 1;
        }
    }
    catch (const std::exception& e) {
        MessageBoxA(nullptr, e.what(), "错误", MB_OK | MB_ICONERROR);
        return 1;
    }

    return 0;
} 