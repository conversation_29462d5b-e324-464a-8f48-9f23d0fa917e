tooltip����
1.tooltip���Զ��������
2.tooltip�����Զ�����

������patch����

//put this class to uibase.h

typedef void (*pfnTimerTick)(LPARAM);

class CTimerWnd : private CWindowWnd
{
public:
	CTimerWnd(){
		m_TimerStarted = FALSE;
		m_TimerTick = NULL;
		m_lParam = 0;
	}

	~CTimerWnd(){
		KillTimer();
	}

	virtual LPCWSTR GetWindowClassName() const{
		return L"TimerWnd";
	}

	bool SetTimer(UINT interval, pfnTimerTick fnTimerTick, LPARAM lParam){
		
		if(EnsureHandle())
		{
			if (m_TimerStarted)
			{
				KillTimer();
				m_TimerStarted = FALSE;
			}

			if (::SetTimer(GetHWND(), 0, interval, NULL))
			{
				m_TimerStarted = TRUE;
			}

			m_TimerTick = fnTimerTick;
			m_lParam = lParam;
		}
		return m_TimerStarted;
	}
	
	void KillTimer(){
		if (m_TimerStarted)
		{
			::KillTimer(GetHWND(), 0);
			m_TimerStarted = FALSE;
			m_TimerTick = NULL;
			m_lParam = 0;
		}
	}
	virtual LRESULT HandleMessage(UINT uMsg, WPARAM wParam, LPARAM lParam){
		if ( uMsg == WM_TIMER)
		{
			if (m_TimerStarted && m_TimerTick)
			{
				m_TimerTick(m_lParam);
			}
			return 0;
		}
		else{
			return __super::HandleMessage(uMsg, wParam, lParam);
		}
	}
protected:
	bool HandleCreated(){
		return GetHWND() != NULL;
	}
	void CreateNativateWindow(){
		if (!HandleCreated())
		{
			Create(HWND_MESSAGE, L"", 0, 0, 0, 0, 0, 0, NULL);
		}
	}
	bool EnsureHandle(){
		if (!HandleCreated())
		{
			CreateNativateWindow();
		}
		return HandleCreated();
	}
private:
	pfnTimerTick	m_TimerTick;
	LPARAM			m_lParam;
	BOOL			m_TimerStarted;
};


//patch for uimanager.h

���ӳ�Ա����
CTimerWnd m_ToolTimerWnd;

���ӳ�Ա����
void HideTooltip();

//patch for uimanager.cpp

//��ʱ���ص�������ȫ��
void TimerTickProc(LPARAM lParam)
{
	CPaintManagerUI* pPM = (CPaintManagerUI*)lParam;
	if (pPM)
	{
		pPM->HideTooltip();
	}
}

//��Ա����
void CPaintManagerUI::HideTooltip()
{
	if (m_hwndTooltip != NULL)
	{
		::SendMessage(m_hwndTooltip, TTM_TRACKACTIVATE, FALSE, (LPARAM)&m_ToolTip);
		m_ToolTimerWnd.KillTimer();
	}
}


��Ϣ����
case WM_MOUSEHOVER:

//ɾ�������ǣ���ᵼ��tooltip���ڸ������
//m_bMouseTracking = false;
if( !sToolTip.IsEmpty() )
{
	//������tooltip��Ϣ�����ö�ʱ��5���رո�tooltip����
	m_ToolTimerWnd.SetTimer(5000, TimerTickProc, (LPARAM)this);
}
break;

case WM_MOUSELEAVE:
			{
			//ֹͣ��ʱ��
				m_ToolTimerWnd.KillTimer();
			
			break;
			

		case WM_MOUSEMOVE:
		{
				CControlUI* pNewHover = FindControl(pt);
				//���ؼ��Ƿ���Եõ������Ϣ������Combo�ĳ�Ա������Ҫ���˵�
				if( pNewHover != NULL && pNewHover->GetManager() != this ) 
					break;

				TEventUI event = { 0 };
				event.ptMouse = pt;
				event.dwTimestamp = ::GetTickCount();
				if( pNewHover != m_pEventHover && m_pEventHover != NULL ) 
				{
					//����Ƶ���һ���ؼ����棬��Ҫ����ǰ�Ŀؼ�����mouseleave��Ϣ��Ȼ��tooltip��������
					event.Type = UIEVENT_MOUSELEAVE;
					event.pSender = m_pEventHover;
					m_pEventHover->Event(event);
					m_pEventHover = NULL;
					if( m_hwndTooltip != NULL )
						::SendMessage(m_hwndTooltip, TTM_TRACKACTIVATE, FALSE, (LPARAM) &m_ToolTip);

				//��Ϊtooltip��Ҫwm_mousehover��Ϣ
					m_bMouseTracking = false;
				}
				
					if( pNewHover != m_pEventHover && pNewHover != NULL ) 
				{
					event.Type = UIEVENT_MOUSEENTER;
					event.pSender = pNewHover;
					pNewHover->Event(event);
					m_pEventHover = pNewHover;
				}
				
				if( m_pEventClick != NULL ) {
					event.Type = UIEVENT_MOUSEMOVE;
					event.pSender = m_pEventClick;
					m_pEventClick->Event(event);
				}
				else if( pNewHover != NULL ) {
					event.Type = UIEVENT_MOUSEMOVE;
					event.pSender = pNewHover;
					pNewHover->Event(event);
				}

				// Start tracking this entire window again...
				if( !m_bMouseTracking && !m_IsDoDragDroping) {
					TRACKMOUSEEVENT tme = { 0 };
					tme.cbSize = sizeof(TRACKMOUSEEVENT);
					tme.dwFlags = TME_HOVER | TME_LEAVE;
					tme.hwndTrack = m_hWndPaint;
					tme.dwHoverTime = m_hwndTooltip == NULL ? 400UL : (DWORD) ::SendMessage(m_hwndTooltip, TTM_GETDELAYTIME, TTDT_INITIAL, 0L);
					_TrackMouseEvent(&tme);
					m_bMouseTracking = true;
				}
		}
		break;
		