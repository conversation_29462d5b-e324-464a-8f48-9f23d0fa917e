cl : 命令行  warning D9035: “Gm”选项已否决，并将在将来的版本中移除
  StdAfx.cpp
F:\Code\test\injector-2\duilib-master\DuiLib\Core\UIManager.h(16,14): warning C4091: “typedef ”: 没有声明变量时忽略“DuiLib::EVENTTYPE_UI”的左侧
  (编译源文件“StdAfx.cpp”)
  
cl : 命令行  warning D9035: “Gm”选项已否决，并将在将来的版本中移除
  UIWebBrowser.cpp
  UIText.cpp
F:\Code\test\injector-2\duilib-master\DuiLib\Control\UIText.cpp(52,37): warning C4302: “类型强制转换”: 从“LPSTR”到“WORD”截断
  (编译源文件“/Control/UIText.cpp”)
  
  UISlider.cpp
F:\Code\test\injector-2\duilib-master\DuiLib\Control\UISlider.cpp(212,36): warning C4302: “类型强制转换”: 从“LPSTR”到“WORD”截断
  (编译源文件“/Control/UISlider.cpp”)
  
  UIScrollBar.cpp
  UIRichEdit.cpp
  UIProgress.cpp
  UIOption.cpp
  UIList.cpp
F:\Code\test\injector-2\duilib-master\DuiLib\Control\UIList.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
F:\Code\test\injector-2\duilib-master\DuiLib\Control\UIList.cpp(900,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“/Control/UIList.cpp”)
  
F:\Code\test\injector-2\duilib-master\DuiLib\Control\UIList.cpp(1749,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“/Control/UIList.cpp”)
  
F:\Code\test\injector-2\duilib-master\DuiLib\Control\UIList.cpp(1781,44): warning C4302: “类型强制转换”: 从“LPSTR”到“WORD”截断
  (编译源文件“/Control/UIList.cpp”)
  
F:\Code\test\injector-2\duilib-master\DuiLib\Control\UIList.cpp(2417,48): warning C4302: “类型强制转换”: 从“LPSTR”到“WORD”截断
  (编译源文件“/Control/UIList.cpp”)
  
  UILabel.cpp
  UIGifAnim.cpp
F:\Code\test\injector-2\duilib-master\DuiLib\Control\UIGifAnim.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  UIEdit.cpp
F:\Code\test\injector-2\duilib-master\DuiLib\Control\UIEdit.cpp(288,35): warning C4302: “类型强制转换”: 从“LPSTR”到“WORD”截断
  (编译源文件“/Control/UIEdit.cpp”)
  
  UIDateTime.cpp
F:\Code\test\injector-2\duilib-master\DuiLib\Control\UIDateTime.cpp(297,35): warning C4302: “类型强制转换”: 从“LPSTR”到“WORD”截断
  (编译源文件“/Control/UIDateTime.cpp”)
  
  UICombo.cpp
  UICheckBox.cpp
  UIButton.cpp
F:\Code\test\injector-2\duilib-master\DuiLib\Control\UIButton.cpp(127,35): warning C4302: “类型强制转换”: 从“LPSTR”到“WORD”截断
  (编译源文件“/Control/UIButton.cpp”)
  
  UIActiveX.cpp
  UIVerticalLayout.cpp
F:\Code\test\injector-2\duilib-master\DuiLib\Layout\UIVerticalLayout.cpp(306,37): warning C4302: “类型强制转换”: 从“LPSTR”到“WORD”截断
  (编译源文件“/Layout/UIVerticalLayout.cpp”)
  
  UITileLayout.cpp
  UITabLayout.cpp
  UIHorizontalLayout.cpp
F:\Code\test\injector-2\duilib-master\DuiLib\Layout\UIHorizontalLayout.cpp(313,37): warning C4302: “类型强制转换”: 从“LPSTR”到“WORD”截断
  (编译源文件“/Layout/UIHorizontalLayout.cpp”)
  
  正在生成代码...
  正在编译...
  UIChildLayout.cpp
  UIRender.cpp
F:\Code\test\injector-2\duilib-master\DuiLib\Core\UIRender.cpp(1196,61): warning C4838: 从“int”转换到“COLOR16”需要收缩转换
  (编译源文件“/Core/UIRender.cpp”)
  
F:\Code\test\injector-2\duilib-master\DuiLib\Core\UIRender.cpp(1196,86): warning C4838: 从“int”转换到“COLOR16”需要收缩转换
  (编译源文件“/Core/UIRender.cpp”)
  
F:\Code\test\injector-2\duilib-master\DuiLib\Core\UIRender.cpp(1196,111): warning C4838: 从“int”转换到“COLOR16”需要收缩转换
  (编译源文件“/Core/UIRender.cpp”)
  
F:\Code\test\injector-2\duilib-master\DuiLib\Core\UIRender.cpp(1197,66): warning C4838: 从“int”转换到“COLOR16”需要收缩转换
  (编译源文件“/Core/UIRender.cpp”)
  
F:\Code\test\injector-2\duilib-master\DuiLib\Core\UIRender.cpp(1197,92): warning C4838: 从“int”转换到“COLOR16”需要收缩转换
  (编译源文件“/Core/UIRender.cpp”)
  
F:\Code\test\injector-2\duilib-master\DuiLib\Core\UIRender.cpp(1197,118): warning C4838: 从“int”转换到“COLOR16”需要收缩转换
  (编译源文件“/Core/UIRender.cpp”)
  
  UIMarkup.cpp
  UIManager.cpp
F:\Code\test\injector-2\duilib-master\DuiLib\Core\UIManager.cpp(1045,39): warning C4838: 从“DWORD”转换到“LONG”需要收缩转换
  (编译源文件“/Core/UIManager.cpp”)
  
F:\Code\test\injector-2\duilib-master\DuiLib\Core\UIManager.cpp(1045,48): warning C4838: 从“DWORD”转换到“LONG”需要收缩转换
  (编译源文件“/Core/UIManager.cpp”)
  
  UIDlgBuilder.cpp
  UIControl.cpp
F:\Code\test\injector-2\duilib-master\DuiLib\Core\UIControl.cpp(832,40): warning C4302: “类型强制转换”: 从“LPSTR”到“WORD”截断
  (编译源文件“/Core/UIControl.cpp”)
  
  UIContainer.cpp
  UIBase.cpp
  WndShadow.cpp
  Utils.cpp
  UIDelegate.cpp
  WinImplBase.cpp
  UIlib.cpp
  UITreeView.cpp
  正在生成代码...
cl : 命令行  warning D9035: “Gm”选项已否决，并将在将来的版本中移除
  stb_image.c
cl : 命令行  warning D9035: “Gm”选项已否决，并将在将来的版本中移除
  XUnzip.cpp
  DuiLib.vcxproj -> F:\Code\test\injector-2\duilib-master\DuiLib\Build\Debug\DuiLib.lib
