<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{20b1c3d9-376c-4de4-b604-48ec3c5c00e2}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{1db34056-bb54-457c-b98c-2e032760fe94}</UniqueIdentifier>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{4fad4cf7-23cc-414e-8d39-b6128b5cc42a}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="debug.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="main_frame.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="StdAfx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="UICrack.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="UIMenu.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="main.cpp">
      <Filter>Header Files</Filter>
    </ClCompile>
    <ClCompile Include="SkinSetting.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="debug.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="main_frame.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="observer_impl_base.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="StdAfx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="UICrack.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="UIMenu.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Resource Files</Filter>
    </ClInclude>
    <ClInclude Include="About.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MenuNotify.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SkinSetting.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="MenuDemo.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="res\menutest.xml">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\skin.xml">
      <Filter>Resource Files</Filter>
    </None>
  </ItemGroup>
</Project>