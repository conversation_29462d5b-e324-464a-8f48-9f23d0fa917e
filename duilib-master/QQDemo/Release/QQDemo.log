  XUnzip.cpp
  chat_dialog.cpp
F:\Code\test\injector-2\duilib-master\DuiLib\Core\UIManager.h(16,14): warning C4091: “typedef ”: 没有声明变量时忽略“DuiLib::EVENTTYPE_UI”的左侧
  (编译源文件“chat_dialog.cpp”)
  
F:\Code\test\injector-2\duilib-master\QQDemo\chat_dialog.cpp(88,37): warning C4477: “sprintf_s”: 格式字符串“%s”需要类型“char *”的参数，但可变参数 1 拥有了类型“DuiLib::CDuiString”
F:\Code\test\injector-2\duilib-master\QQDemo\chat_dialog.cpp(191,36): warning C4477: “sprintf_s”: 格式字符串“%s”需要类型“char *”的参数，但可变参数 1 拥有了类型“DuiLib::CDuiString”
F:\Code\test\injector-2\duilib-master\QQDemo\chat_dialog.cpp(203,36): warning C4477: “sprintf_s”: 格式字符串“%s”需要类型“char *”的参数，但可变参数 1 拥有了类型“DuiLib::CDuiString”
  ColorPicker.cpp
F:\Code\test\injector-2\duilib-master\DuiLib\Core\UIManager.h(16,14): warning C4091: “typedef ”: 没有声明变量时忽略“DuiLib::EVENTTYPE_UI”的左侧
  (编译源文件“ColorPicker.cpp”)
  
  color_skin.cpp
F:\Code\test\injector-2\duilib-master\DuiLib\Core\UIManager.h(16,14): warning C4091: “typedef ”: 没有声明变量时忽略“DuiLib::EVENTTYPE_UI”的左侧
  (编译源文件“color_skin.cpp”)
  
  debug.cpp
F:\Code\test\injector-2\duilib-master\DuiLib\Core\UIManager.h(16,14): warning C4091: “typedef ”: 没有声明变量时忽略“DuiLib::EVENTTYPE_UI”的左侧
  (编译源文件“debug.cpp”)
  
F:\Code\test\injector-2\duilib-master\QQDemo\debug.cpp(97,2): warning C4996: 'GetVersionExA': 被声明为已否决
  main.cpp
F:\Code\test\injector-2\duilib-master\DuiLib\Core\UIManager.h(16,14): warning C4091: “typedef ”: 没有声明变量时忽略“DuiLib::EVENTTYPE_UI”的左侧
  (编译源文件“main.cpp”)
  
  main_frame.cpp
F:\Code\test\injector-2\duilib-master\DuiLib\Core\UIManager.h(16,14): warning C4091: “typedef ”: 没有声明变量时忽略“DuiLib::EVENTTYPE_UI”的左侧
  (编译源文件“main_frame.cpp”)
  
  StdAfx.cpp
F:\Code\test\injector-2\duilib-master\DuiLib\Core\UIManager.h(16,14): warning C4091: “typedef ”: 没有声明变量时忽略“DuiLib::EVENTTYPE_UI”的左侧
  (编译源文件“StdAfx.cpp”)
  
  UIFriends.cpp
F:\Code\test\injector-2\duilib-master\DuiLib\Core\UIManager.h(16,14): warning C4091: “typedef ”: 没有声明变量时忽略“DuiLib::EVENTTYPE_UI”的左侧
  (编译源文件“UIFriends.cpp”)
  
F:\Code\test\injector-2\duilib-master\QQDemo\UIFriends.cpp(249,37): warning C4477: “sprintf_s”: 格式字符串“%s”需要类型“char *”的参数，但可变参数 1 拥有了类型“const DuiLib::CDuiString”
F:\Code\test\injector-2\duilib-master\QQDemo\UIFriends.cpp(288,36): warning C4477: “sprintf_s”: 格式字符串“%s”需要类型“char *”的参数，但可变参数 1 拥有了类型“const DuiLib::CDuiString”
F:\Code\test\injector-2\duilib-master\QQDemo\UIFriends.cpp(311,37): warning C4477: “sprintf_s”: 格式字符串“%s”需要类型“char *”的参数，但可变参数 1 拥有了类型“const DuiLib::CDuiString”
  UIGroups.cpp
F:\Code\test\injector-2\duilib-master\DuiLib\Core\UIManager.h(16,14): warning C4091: “typedef ”: 没有声明变量时忽略“DuiLib::EVENTTYPE_UI”的左侧
  (编译源文件“UIGroups.cpp”)
  
F:\Code\test\injector-2\duilib-master\QQDemo\UIGroups.cpp(246,37): warning C4477: “sprintf_s”: 格式字符串“%s”需要类型“char *”的参数，但可变参数 1 拥有了类型“const DuiLib::CDuiString”
F:\Code\test\injector-2\duilib-master\QQDemo\UIGroups.cpp(285,36): warning C4477: “sprintf_s”: 格式字符串“%s”需要类型“char *”的参数，但可变参数 1 拥有了类型“const DuiLib::CDuiString”
F:\Code\test\injector-2\duilib-master\QQDemo\UIGroups.cpp(308,37): warning C4477: “sprintf_s”: 格式字符串“%s”需要类型“char *”的参数，但可变参数 1 拥有了类型“const DuiLib::CDuiString”
  UIListCommonDefine.cpp
F:\Code\test\injector-2\duilib-master\DuiLib\Core\UIManager.h(16,14): warning C4091: “typedef ”: 没有声明变量时忽略“DuiLib::EVENTTYPE_UI”的左侧
  (编译源文件“UIListCommonDefine.cpp”)
  
  UIMicroBlog.cpp
F:\Code\test\injector-2\duilib-master\DuiLib\Core\UIManager.h(16,14): warning C4091: “typedef ”: 没有声明变量时忽略“DuiLib::EVENTTYPE_UI”的左侧
  (编译源文件“UIMicroBlog.cpp”)
  
  正在生成代码...
QQDemo.rc(10): fatal error RC1015: cannot open include file 'afxres.h'.
  
