#pragma once
#include <windows.h>
#include <TlHelp32.h>
#include <vector>
#include <string>
#include <Psapi.h>


//ȷ������paspi.lib
#pragma comment(lib,"psapi.lib")


struct ProcessInfo {
	DWORD processId;
	std::wstring processName;
};


class ProcessManager {
public:
	static std::vector<ProcessInfo> GetProcessList();
	static BOOL InjectDll(DWORD processId, const std::wstring& dllPath);
	static BOOL UnloadDll(DWORD processId, const std::wstring& dllPath);	//ж�ط���
	static BOOL CloseProcess(DWORD processId);
private:

};