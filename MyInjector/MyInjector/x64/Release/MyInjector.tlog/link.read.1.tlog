^F:\CODE\TEST\INJECTOR-2\MYINJECTOR\MYINJECTOR\X64\RELEASE\INJECTOR.OBJ|F:\CODE\TEST\INJECTOR-2\MYINJECTOR\MYINJECTOR\X64\RELEASE\MAINDIALOG.OBJ|F:\CODE\TEST\INJECTOR-2\MYINJECTOR\MYINJECTOR\X64\RELEASE\MYINJECTOR.OBJ|F:\CODE\TEST\INJECTOR-2\MYINJECTOR\MYINJECTOR\X64\RELEASE\MYINJECTOR.RES|F:\CODE\TEST\INJECTOR-2\MYINJECTOR\MYINJECTOR\X64\RELEASE\PROCESSMANAGER.OBJ
C:\PROGRAM FILES (X86)\WINDOWS KITS\10\LIB\10.0.26100.0\UM\X64\KERNEL32.LIB
C:\PROGRAM FILES (X86)\WINDOWS KITS\10\LIB\10.0.26100.0\UM\X64\USER32.LIB
C:\PROGRAM FILES (X86)\WINDOWS KITS\10\LIB\10.0.26100.0\UM\X64\GDI32.LIB
C:\PROGRAM FILES (X86)\WINDOWS KITS\10\LIB\10.0.26100.0\UM\X64\WINSPOOL.LIB
C:\PROGRAM FILES (X86)\WINDOWS KITS\10\LIB\10.0.26100.0\UM\X64\COMDLG32.LIB
C:\PROGRAM FILES (X86)\WINDOWS KITS\10\LIB\10.0.26100.0\UM\X64\ADVAPI32.LIB
C:\PROGRAM FILES (X86)\WINDOWS KITS\10\LIB\10.0.26100.0\UM\X64\SHELL32.LIB
C:\PROGRAM FILES (X86)\WINDOWS KITS\10\LIB\10.0.26100.0\UM\X64\OLE32.LIB
C:\PROGRAM FILES (X86)\WINDOWS KITS\10\LIB\10.0.26100.0\UM\X64\OLEAUT32.LIB
C:\PROGRAM FILES (X86)\WINDOWS KITS\10\LIB\10.0.26100.0\UM\X64\UUID.LIB
C:\PROGRAM FILES (X86)\WINDOWS KITS\10\LIB\10.0.26100.0\UM\X64\ODBC32.LIB
C:\PROGRAM FILES (X86)\WINDOWS KITS\10\LIB\10.0.26100.0\UM\X64\ODBCCP32.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\ABSEIL_DLL.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\ABSL_DECODE_RUST_PUNYCODE.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\ABSL_DEMANGLE_RUST.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\ABSL_FLAGS_COMMANDLINEFLAG.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\ABSL_FLAGS_COMMANDLINEFLAG_INTERNAL.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\ABSL_FLAGS_CONFIG.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\ABSL_FLAGS_INTERNAL.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\ABSL_FLAGS_MARSHALLING.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\ABSL_FLAGS_PARSE.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\ABSL_FLAGS_PRIVATE_HANDLE_ACCESSOR.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\ABSL_FLAGS_PROGRAM_NAME.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\ABSL_FLAGS_REFLECTION.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\ABSL_FLAGS_USAGE.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\ABSL_FLAGS_USAGE_INTERNAL.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\ABSL_LOG_FLAGS.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\ABSL_LOG_INTERNAL_STRUCTURED_PROTO.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\ABSL_POISON.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\ABSL_TRACING_INTERNAL.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\ABSL_UTF8_FOR_CODE_POINT.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\ADDRESS_SORTING.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\BOOST_CONTAINER-VC143-MT-X64-1_86.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\BOOST_CONTEXT-VC143-MT-X64-1_86.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\BOOST_COROUTINE-VC143-MT-X64-1_86.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\BOOST_DATE_TIME-VC143-MT-X64-1_86.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\BOOST_REGEX-VC143-MT-X64-1_86.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\BOOST_SYSTEM-VC143-MT-X64-1_86.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\CARES.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\DETOURS.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\FMT.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\GPR.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\GRPC++.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\GRPC++_ALTS.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\GRPC++_ERROR_DETAILS.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\GRPC++_REFLECTION.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\GRPC++_UNSECURE.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\GRPC.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\GRPCPP_CHANNELZ.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\GRPC_AUTHORIZATION_PROVIDER.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\GRPC_PLUGIN_SUPPORT.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\GRPC_UNSECURE.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\LIBCRYPTO.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\LIBPROTOBUF-LITE.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\LIBPROTOBUF.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\LIBPROTOC.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\LIBSSL.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\MONGOOSE.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\RE2.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\SPDLOG.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\TINYXML2.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\UPB_BASE_LIB.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\UPB_JSON_LIB.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\UPB_MEM_LIB.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\UPB_MESSAGE_LIB.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\UPB_MINI_DESCRIPTOR_LIB.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\UPB_TEXTFORMAT_LIB.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\UPB_WIRE_LIB.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\UTF8_RANGE.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\UTF8_VALIDITY.LIB
E:\VCPKG\INSTALLED\X64-WINDOWS\LIB\ZLIB.LIB
F:\CODE\TEST\INJECTOR-2\MYINJECTOR\MYINJECTOR\X64\RELEASE\MYINJECTOR.RES
F:\CODE\TEST\INJECTOR-2\MYINJECTOR\MYINJECTOR\X64\RELEASE\INJECTOR.OBJ
F:\CODE\TEST\INJECTOR-2\MYINJECTOR\MYINJECTOR\X64\RELEASE\MAINDIALOG.OBJ
F:\CODE\TEST\INJECTOR-2\MYINJECTOR\MYINJECTOR\X64\RELEASE\MYINJECTOR.OBJ
F:\CODE\TEST\INJECTOR-2\MYINJECTOR\MYINJECTOR\X64\RELEASE\PROCESSMANAGER.OBJ
C:\WINDOWS\GLOBALIZATION\SORTING\SORTDEFAULT.NLS
C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\PROFESSIONAL\VC\TOOLS\MSVC\14.42.34433\BIN\HOSTX64\X64\C2.DLL
C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\PROFESSIONAL\VC\TOOLS\MSVC\14.42.34433\LIB\X64\MSVCPRT.LIB
C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\PROFESSIONAL\VC\TOOLS\MSVC\14.42.34433\LIB\X64\MSVCRT.LIB
C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\PROFESSIONAL\VC\TOOLS\MSVC\14.42.34433\LIB\X64\OLDNAMES.LIB
C:\PROGRAM FILES (X86)\WINDOWS KITS\10\LIB\10.0.26100.0\UM\X64\PSAPI.LIB
C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\PROFESSIONAL\VC\TOOLS\MSVC\14.42.34433\LIB\X64\VCRUNTIME.LIB
C:\PROGRAM FILES (X86)\WINDOWS KITS\10\LIB\10.0.26100.0\UCRT\X64\UCRT.LIB
C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\PROFESSIONAL\VC\TOOLS\MSVC\14.42.34433\BIN\HOSTX64\X64\2052\LINKUI.DLL
C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\PROFESSIONAL\VC\TOOLS\MSVC\14.42.34433\BIN\HOSTX64\X64\CVTRES.EXE
C:\WINDOWS\SYSTEM32\TZRES.DLL
F:\CODE\TEST\INJECTOR-2\MYINJECTOR\MYINJECTOR\X64\RELEASE\VC143.PDB
