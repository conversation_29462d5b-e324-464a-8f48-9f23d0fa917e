^F:\CODE\TEST\INJECTOR-2\MYINJECTOR\MYINJECTOR\X64\DEBUG\INJECTOR.OBJ|F:\CODE\TEST\INJECTOR-2\MYINJECTOR\MYINJECTOR\X64\DEBUG\MAINDIALOG.OBJ|F:\CODE\TEST\INJECTOR-2\MYINJECTOR\MYINJECTOR\X64\DEBUG\MYINJECTOR.OBJ|F:\CODE\TEST\INJECTOR-2\MYINJECTOR\MYINJECTOR\X64\DEBUG\MYINJECTOR.RES|F:\CODE\TEST\INJECTOR-2\MYINJECTOR\MYINJECTOR\X64\DEBUG\PROCESSMANAGER.OBJ
/OUT:"F:\CODE\TEST\INJECTOR-2\MYINJECTOR\X64\DEBUG\MYINJECTOR.EXE" /INCREMENTAL /ILK:"MYINJECTOR\X64\DEBUG\MYINJECTOR.ILK" /NOLOGO /LIBPATH:"E:\VCPKG\INSTALLED\X64-WINDOWS\DEBUG\LIB" /LIBPATH:"E:\VCPKG\INSTALLED\X64-WINDOWS\DEBUG\LIB\MANUAL-LINK" KERNEL32.LIB USER32.LIB GDI32.LIB WINSPOOL.LIB COMDLG32.LIB ADVAPI32.LIB SHELL32.LIB OLE32.LIB OLEAUT32.LIB UUID.LIB ODBC32.LIB ODBCCP32.LIB "E:\VCPKG\INSTALLED\X64-WINDOWS\DEBUG\LIB\*.LIB" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"F:\CODE\TEST\INJECTOR-2\MYINJECTOR\X64\DEBUG\MYINJECTOR.PDB" /SUBSYSTEM:WINDOWS /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"F:\CODE\TEST\INJECTOR-2\MYINJECTOR\X64\DEBUG\MYINJECTOR.LIB" /MACHINE:X64 MYINJECTOR\X64\DEBUG\MYINJECTOR.RES
MYINJECTOR\X64\DEBUG\INJECTOR.OBJ
MYINJECTOR\X64\DEBUG\MAINDIALOG.OBJ
MYINJECTOR\X64\DEBUG\MYINJECTOR.OBJ
MYINJECTOR\X64\DEBUG\PROCESSMANAGER.OBJ
