#include "WeChatHelper.h"
#include <TlHelp32.h>
#include <filesystem>

// 全局变量定义
uintptr_t weChatWinAddress = 0;
HWND globalHwnd = NULL;

/*
获取WeChatWin.dll的版本号
*/
std::wstring GetWeChatWinVersion()
{
    wchar_t dllPath[MAX_PATH] = { 0 };
    DWORD size = GetModuleFileName(reinterpret_cast<HMODULE>(weChatWinAddress), dllPath, MAX_PATH);
    if (size == 0) {
        return L"Unknown";
    }

    DWORD verHandle = 0;
    DWORD verSize = GetFileVersionInfoSize(dllPath, &verHandle);
    if (verSize == 0) {
        return L"Unknown";
    }

    std::vector<BYTE> verData(verSize);
    if (!GetFileVersionInfo(dllPath, verHandle, verSize, verData.data())) {
        return L"Unknown";
    }

    LPVOID lpBuffer = nullptr;
    UINT size2 = 0;
    if (!VerQueryValue(verData.data(), L"\\", &lpBuffer, &size2)) {
        return L"Unknown";
    }

    VS_FIXEDFILEINFO* verInfo = static_cast<VS_FIXEDFILEINFO*>(lpBuffer);
    if (verInfo->dwSignature != 0xfeef04bd) {
        return L"Unknown";
    }

    // 格式化版本号
    wchar_t version[50] = { 0 };
    swprintf_s(version, L"%d.%d.%d.%d",
        HIWORD(verInfo->dwFileVersionMS),
        LOWORD(verInfo->dwFileVersionMS),
        HIWORD(verInfo->dwFileVersionLS),
        LOWORD(verInfo->dwFileVersionLS));

    return version;
}

/*
获取微信基址
*/
uintptr_t GetWechatWinAddress()
{
    if (weChatWinAddress == 0) {
        HMODULE winAddress = GetModuleHandle(L"WeChatWin.dll");
        if (!winAddress) {
            winAddress = LoadLibrary(L"WeChatWin.dll");
        }
        weChatWinAddress = reinterpret_cast<uintptr_t>(winAddress);
    }
    return weChatWinAddress;
}

/*
获取微信窗口句柄
*/
HWND GetWeChatWindow()
{
    if (globalHwnd == NULL) {
        globalHwnd = FindWindow(L"WeChatMainWndForPC", NULL);
    }
    return globalHwnd;
}

/*
初始化微信相关数据
*/
bool InitWeChatData()
{
    // 获取微信基址
    uintptr_t baseAddr = GetWechatWinAddress();
    if (baseAddr == 0) {
        return false;
    }

    // 获取微信窗口句柄
    HWND hwnd = GetWeChatWindow();
    if (hwnd == NULL) {
        return false;
    }

    return true;
} 