#pragma once
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif // !WIN32_LEAN_AND_MEAN
#ifndef NOMINMAX
#define NOMINMAX
#endif // !NOMINMAX

#include <WinSock2.h>
#include <Windows.h>
#include <boost/asio.hpp>
#include <string>
#include <thread>
#include <functional>
#include <memory>
#include "../Common/message.h"

class TcpClient {
public:
    using message_handler = std::function<void(const Message&)>;

    TcpClient(const std::string& host, unsigned short port);
    ~TcpClient();

    bool connect();
    void disconnect();
    bool send_message(const Message& msg);
    void set_message_handler(message_handler handler);
    bool is_connected() const { return connected_; }
    void start_receive();

private:
    void handle_read(const boost::system::error_code& error, size_t bytes_transferred);
    void handle_write(const boost::system::error_code& error);
    void do_connect();
    void reset_socket();

    boost::asio::io_context io_context_;
    boost::asio::ip::tcp::socket socket_;
    boost::asio::ip::tcp::endpoint endpoint_;
    boost::asio::streambuf read_buffer_;
    std::vector<char> write_buffer_;
    message_handler msg_handler_;
    bool connected_;
    std::unique_ptr<std::thread> io_thread_;
    bool should_stop_;
}; 